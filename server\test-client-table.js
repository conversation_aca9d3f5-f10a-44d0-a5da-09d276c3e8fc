const { Pool } = require('pg');

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',        // Votre nom d'utilisateur PostgreSQL
  host: 'localhost',       // Adresse du serveur PostgreSQL
  database: 'Facutration', // Nom de votre base de données (avec 'u')
  password: '123456',      // Votre mot de passe PostgreSQL
  port: 5432,             // Port PostgreSQL par défaut
});

async function testClientTable() {
  console.log('🔍 Test de la table Client dans la base "Facutration"...\n');

  try {
    // Test de connexion
    console.log('1️⃣ Test de connexion à la base de données...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie à la base "Facutration"\n');

    // Vérifier si la table Client existe
    console.log('2️⃣ Vérification de l\'existence de la table Client...');
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'client'
      );
    `;
    
    const tableExistsResult = await client.query(tableExistsQuery);
    const tableExists = tableExistsResult.rows[0].exists;
    
    if (tableExists) {
      console.log('✅ Table "client" trouvée\n');
    } else {
      console.log('❌ Table "client" non trouvée');
      console.log('💡 Vérifiez que la table a été créée avec le bon nom\n');
      
      // Lister toutes les tables disponibles
      console.log('📋 Tables disponibles dans la base "Facutration":');
      const tablesQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
      `;
      const tablesResult = await client.query(tablesQuery);
      tablesResult.rows.forEach(row => {
        console.log(`   - ${row.table_name}`);
      });
      
      client.release();
      return;
    }

    // Vérifier la structure de la table
    console.log('3️⃣ Vérification de la structure de la table Client...');
    const structureQuery = `
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'client'
      ORDER BY ordinal_position;
    `;
    
    const structureResult = await client.query(structureQuery);
    console.log('📋 Structure de la table "client":');
    structureResult.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    console.log('');

    // Compter le nombre de clients
    console.log('4️⃣ Comptage des clients...');
    const countQuery = 'SELECT COUNT(*) as total FROM client';
    const countResult = await client.query(countQuery);
    const totalClients = countResult.rows[0].total;
    console.log(`📊 Nombre total de clients: ${totalClients}\n`);

    if (totalClients > 0) {
      // Afficher quelques exemples de clients
      console.log('5️⃣ Exemples de clients dans la table:');
      const clientsQuery = `
        SELECT idclient, nom, prenom, adresse, ville, tel, email, ids 
        FROM client 
        ORDER BY idclient 
        LIMIT 5
      `;
      const clientsResult = await client.query(clientsQuery);
      
      clientsResult.rows.forEach((client, index) => {
        console.log(`   ${index + 1}. ID: ${client.idclient}`);
        console.log(`      Nom: ${client.nom} ${client.prenom}`);
        console.log(`      Adresse: ${client.adresse}`);
        console.log(`      Ville: ${client.ville || 'Non renseignée'}`);
        console.log(`      Téléphone: ${client.tel || 'Non renseigné'}`);
        console.log(`      Email: ${client.email || 'Non renseigné'}`);
        console.log(`      Secteur ID: ${client.ids || 'Non renseigné'}`);
        console.log('      ───────────────────────────────────────');
      });
      console.log('');

      // Test de la requête utilisée par l'API
      console.log('6️⃣ Test de la requête API avec JOIN...');
      const apiQuery = `
        SELECT
          c.idclient,
          c.nom,
          c.prenom,
          c.adresse,
          c.ville,
          c.tel,
          c.email,
          c.ids,
          s.nom as secteur_nom
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        ORDER BY c.nom, c.prenom
        LIMIT 3
      `;
      
      try {
        const apiResult = await client.query(apiQuery);
        console.log('✅ Requête API réussie !');
        console.log(`📊 ${apiResult.rows.length} client(s) récupéré(s) avec JOIN`);
        
        apiResult.rows.forEach((client, index) => {
          console.log(`   ${index + 1}. ${client.nom} ${client.prenom}`);
          console.log(`      Secteur: ${client.secteur_nom || 'Non trouvé'} (ID: ${client.ids})`);
        });
      } catch (joinError) {
        console.log('⚠️ Erreur avec la requête JOIN (table secteur manquante?)');
        console.log(`📝 Erreur: ${joinError.message}`);
        
        // Test sans JOIN
        console.log('🔄 Test sans JOIN...');
        const simpleQuery = `
          SELECT idclient, nom, prenom, adresse, ville, tel, email, ids
          FROM client
          ORDER BY nom, prenom
          LIMIT 3
        `;
        const simpleResult = await client.query(simpleQuery);
        console.log(`✅ ${simpleResult.rows.length} client(s) récupéré(s) sans JOIN`);
      }

    } else {
      console.log('❌ La table "client" est vide');
      console.log('💡 Ajoutez des clients à la table pour tester l\'affichage');
    }

    client.release();
    console.log('\n🎉 Test terminé avec succès !');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    console.error('📋 Détails:', error);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testClientTable();
