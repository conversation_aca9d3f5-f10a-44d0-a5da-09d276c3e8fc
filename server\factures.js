const express = require('express');
const { pool } = require('./database');

const router = express.Router();

// Route pour récupérer toutes les factures
router.get('/api/factures', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/factures');

    const query = `
      SELECT 
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cont.reference as contrat_reference,
        s.nom as secteur_nom
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY f.date DESC, f.idfact DESC
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} factures récupérées`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} facture(s) trouvée(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion au serveur de factures',
      error: error.message
    });
  }
});

// Route pour récupérer une facture spécifique par ID
router.get('/api/factures/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/factures/${id}`);

    const query = `
      SELECT 
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        c.tel as client_tel,
        c.email as client_email,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cont.reference as contrat_reference,
        s.nom as secteur_nom
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE f.idfact = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    console.log(`✅ Facture ${id} récupérée`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Facture trouvée'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la facture',
      error: error.message
    });
  }
});

// Route pour créer une nouvelle facture
router.post('/api/factures', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/factures:', req.body);
    const { date, idcons, montant, periode, reference, status = 'nonpayée' } = req.body;

    // Validation des champs requis
    if (!date || !idcons || !montant || !periode || !reference) {
      return res.status(400).json({
        success: false,
        message: 'Les champs date, idcons, montant, periode et reference sont requis'
      });
    }

    const query = `
      INSERT INTO facture (date, idcons, montant, periode, reference, status)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `;

    const values = [date, idcons, montant, periode, reference, status];
    const result = await pool.query(query, values);

    console.log('✅ Nouvelle facture créée:', result.rows[0]);
    res.status(201).json({
      success: true,
      data: result.rows[0],
      message: 'Facture créée avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la facture',
      error: error.message
    });
  }
});

// Route pour mettre à jour le statut d'une facture
router.put('/api/factures/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    console.log(`📥 Requête PUT /api/factures/${id}/status:`, { status });

    // Validation du statut
    if (!['payée', 'nonpayée'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Le statut doit être "payée" ou "nonpayée"'
      });
    }

    const query = `
      UPDATE facture 
      SET status = $1
      WHERE idfact = $2
      RETURNING *
    `;

    const result = await pool.query(query, [status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Facture non trouvée'
      });
    }

    console.log('✅ Statut de la facture mis à jour:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Statut de la facture mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du statut de la facture:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du statut de la facture',
      error: error.message
    });
  }
});

// Route pour récupérer les factures par statut
router.get('/api/factures/status/:status', async (req, res) => {
  try {
    const { status } = req.params;
    console.log(`📥 Requête GET /api/factures/status/${status}`);

    // Validation du statut
    if (!['payée', 'nonpayée', 'toutes'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Le statut doit être "payée", "nonpayée" ou "toutes"'
      });
    }

    let query = `
      SELECT 
        f.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        cons.consommationpre,
        cons.consommationactuelle,
        cont.reference as contrat_reference
      FROM facture f
      LEFT JOIN consommation cons ON f.idcons = cons.idcons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
    `;

    let values = [];
    if (status !== 'toutes') {
      query += ' WHERE f.status = $1';
      values = [status];
    }

    query += ' ORDER BY f.date DESC, f.idfact DESC';

    const result = await pool.query(query, values);

    console.log(`✅ ${result.rows.length} factures trouvées avec le statut "${status}"`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} facture(s) trouvée(s) avec le statut "${status}"`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des factures par statut:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des factures par statut',
      error: error.message
    });
  }
});

// Route pour calculer les statistiques des factures
router.get('/api/factures/stats/summary', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/factures/stats/summary');

    const query = `
      SELECT 
        COUNT(*) as total_factures,
        COUNT(CASE WHEN status = 'payée' THEN 1 END) as factures_payees,
        COUNT(CASE WHEN status = 'nonpayée' THEN 1 END) as factures_non_payees,
        COALESCE(SUM(montant), 0) as montant_total,
        COALESCE(SUM(CASE WHEN status = 'payée' THEN montant ELSE 0 END), 0) as montant_paye,
        COALESCE(SUM(CASE WHEN status = 'nonpayée' THEN montant ELSE 0 END), 0) as montant_impaye
      FROM facture
    `;

    const result = await pool.query(query);

    console.log('✅ Statistiques des factures calculées');
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Statistiques des factures récupérées'
    });

  } catch (error) {
    console.error('❌ Erreur lors du calcul des statistiques des factures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du calcul des statistiques des factures',
      error: error.message
    });
  }
});

module.exports = router;
