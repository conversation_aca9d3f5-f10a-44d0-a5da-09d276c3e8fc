require('dotenv').config();
const { Pool } = require('pg');

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Variables globales pour stocker les informations de la base
let databaseTables = [];
let databaseSchema = {};

// Fonction pour détecter toutes les tables de la base de données
async function detectDatabaseTables() {
  try {
    console.log('🔍 Détection des tables de la base de données...');

    const result = await pool.query(`
      SELECT
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns
      WHERE table_schema = 'public'
      ORDER BY table_name, ordinal_position
    `);

    // Organiser les colonnes par table
    const tableSchema = {};
    result.rows.forEach(row => {
      if (!tableSchema[row.table_name]) {
        tableSchema[row.table_name] = [];
      }
      tableSchema[row.table_name].push({
        column: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default
      });
    });

    // Mettre à jour les variables globales
    databaseTables = Object.keys(tableSchema);
    databaseSchema = tableSchema;

    console.log(`✅ ${databaseTables.length} tables détectées:`, databaseTables);
    return tableSchema;

  } catch (error) {
    console.error('❌ Erreur lors de la détection des tables:', error);
    throw error;
  }
}

// Fonction pour obtenir le nombre d'enregistrements dans chaque table
async function getTableCounts() {
  try {
    const counts = {};
    
    for (const tableName of databaseTables) {
      try {
        const result = await pool.query(`SELECT COUNT(*) as count FROM "${tableName}"`);
        counts[tableName] = parseInt(result.rows[0].count);
      } catch (error) {
        console.warn(`⚠️ Impossible de compter les enregistrements de la table ${tableName}:`, error.message);
        counts[tableName] = 0;
      }
    }
    
    return counts;
  } catch (error) {
    console.error('❌ Erreur lors du comptage des tables:', error);
    throw error;
  }
}

// Fonction d'initialisation de la base de données
async function initializeDatabase() {
  try {
    console.log('🚀 Initialisation de la connexion à la base de données...');
    
    // Test de connexion
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie');
    client.release();
    
    // Détection automatique des tables
    await detectDatabaseTables();
    
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation de la base de données:', error);
    throw error;
  }
}

module.exports = {
  pool,
  detectDatabaseTables,
  getTableCounts,
  initializeDatabase,
  getDatabaseTables: () => databaseTables,
  getDatabaseSchema: () => databaseSchema
};
