const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3002;

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',        // Votre nom d'utilisateur PostgreSQL
  host: 'localhost',       // Adresse du serveur PostgreSQL
  database: 'Facutration', // Nom de votre base de données (avec 'u')
  password: '123456',      // Votre mot de passe PostgreSQL
  port: 5432,             // Port PostgreSQL par défaut
});

// Test de connexion à la base de données
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
    console.log('💡 Vérifiez que PostgreSQL est démarré et que la base "Facutration" existe');
  } else {
    console.log('✅ Connexion réussie à la base de données "Facutration"');
    release();
  }
});

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur de login avec base de données...');

// Route de test
app.get('/', (req, res) => {
  console.log('📡 Route / appelée');
  res.json({
    message: 'Serveur de login fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de login avec base de données PostgreSQL
app.post('/login', async (req, res) => {
  console.log('📥 Requête POST /login:', req.body);
  const { email, motDepass } = req.body;

  // Validation des champs requis
  if (!email || !motDepass) {
    return res.status(400).json({
      success: false,
      message: "Email et mot de passe requis"
    });
  }

  try {
    console.log('🔍 Recherche utilisateur dans la base "Facutration"...');
    console.log(`📧 Email: ${email}`);

    // Requête SQL pour chercher l'utilisateur dans la table "utilisateur"
    const query = `
      SELECT idtech, nom, prenom, adresse, tel, email, role
      FROM utilisateur
      WHERE email = $1 AND motdepass = $2
    `;

    console.log('🔍 Exécution de la requête SQL...');
    const result = await pool.query(query, [email, motDepass]);

    console.log(`📊 Résultats trouvés: ${result.rows.length}`);

    if (result.rows.length > 0) {
      const user = result.rows[0];
      console.log('✅ Utilisateur trouvé:', {
        idtech: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      });

      return res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          idtech: user.idtech,
          nom: user.nom,
          prenom: user.prenom,
          email: user.email,
          role: user.role,
          adresse: user.adresse,
          tel: user.tel
        }
      });
    } else {
      console.log('❌ Aucun utilisateur trouvé avec ces identifiants');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la connexion à la base de données:', error.message);
    console.error('📋 Détails de l\'erreur:', error);

    // Mode fallback - vérification locale en cas d'erreur de base de données
    console.log('🔄 Activation du mode fallback...');

    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      console.log('✅ Connexion fallback réussie pour le technicien');
      return res.json({
        success: true,
        message: 'Connexion réussie (mode fallback)',
        user: {
          idtech: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Tech',
          adresse: '123 Rue Test',
          tel: '0123456789'
        }
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données'
    });
  }
});

// Route pour récupérer la dernière consommation (pour la page consommation)
app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 GET /api/last-consommation-global');
  res.json({
    success: true,
    message: 'Dernière consommation récupérée avec succès',
    data: {
      idcons: 18,
      consommationpre: 122,
      consommationactuelle: 135,
      idcont: 10,
      idtech: 1,
      idtranch: 1,
      jours: 30,
      periode: "juin 2025",
      status: "nouveau"
    }
  });
});

// Route pour récupérer les contrats
app.get('/api/contracts', (req, res) => {
  console.log('📥 GET /api/contracts');
  res.json({
    success: true,
    message: 'Contrats récupérés avec succès',
    count: 1,
    data: [{
      idcontract: 10,
      codeqr: "QR123",
      datecontract: "2024-01-15T09:00:00.000Z",
      idclient: 3,
      marquecompteur: "Sagemcom",
      nom: "Dupont",
      prenom: "Jean",
      ville: "Paris"
    }]
  });
});

// Route POST pour enregistrer une consommation
app.post('/api/consommations', (req, res) => {
  console.log('📝 POST /api/consommations');
  console.log('Données reçues:', req.body);
  
  const newConsommation = {
    idcons: Math.floor(Math.random() * 1000) + 100,
    consommationpre: req.body.consommationpre || 0,
    consommationactuelle: req.body.consommationactuelle,
    idcont: req.body.idcont,
    idtech: req.body.idtech || 1,
    idtranch: 1,
    jours: req.body.jours || 30,
    periode: req.body.periode || new Date().toISOString().slice(0, 7),
    status: 'nouveau',
    timestamp: new Date().toISOString()
  };
  
  console.log('✅ Consommation simulée:', newConsommation);
  
  res.json({
    success: true,
    message: 'Consommation enregistrée avec succès',
    data: newConsommation
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur de login démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - POST /login (authentification)');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
  console.log('🔑 Comptes disponibles:');
  console.log('  - <EMAIL> / Tech123 (Technicien)');
  console.log('  - <EMAIL> / admin123 (Admin)');
  console.log('🎯 Le serveur est prêt !');
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
