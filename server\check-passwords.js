const { Pool } = require('pg');

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',        // Votre nom d'utilisateur PostgreSQL
  host: 'localhost',       // Adresse du serveur PostgreSQL
  database: 'Facutration', // Nom de votre base de données (avec 'u')
  password: '123456',      // Votre mot de passe PostgreSQL
  port: 5432,             // Port PostgreSQL par défaut
});

async function checkPasswords() {
  console.log('🔍 Vérification des mots de passe dans la table Utilisateur...\n');

  try {
    const client = await pool.connect();
    console.log('✅ Connexion réussie à la base "Facutration"\n');

    // Récupérer tous les utilisateurs avec leurs mots de passe
    const query = `
      SELECT idtech, nom, prenom, email, motdepass, role 
      FROM utilisateur 
      ORDER BY idtech
    `;
    
    const result = await client.query(query);
    
    console.log('👥 Utilisateurs dans la base de données:');
    console.log('═══════════════════════════════════════════════════════════════');
    
    result.rows.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.idtech}`);
      console.log(`   Nom: ${user.nom} ${user.prenom}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Mot de passe: ${user.motdepass}`);
      console.log(`   Rôle: ${user.role}`);
      console.log('   ───────────────────────────────────────────────────────────');
    });

    console.log('\n🔑 Comptes de test pour la connexion:');
    result.rows.forEach((user) => {
      const roleIcon = user.role === 'Tech' ? '👨‍🔧' : '👨‍💼';
      console.log(`   ${roleIcon} ${user.role}: ${user.email} / ${user.motdepass}`);
    });

    client.release();
    console.log('\n✅ Vérification terminée !');

  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error.message);
  } finally {
    await pool.end();
  }
}

// Exécuter la vérification
checkPasswords();
