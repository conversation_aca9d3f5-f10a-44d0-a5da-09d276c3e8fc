const axios = require('axios');

async function testClientSelection() {
  console.log('🧪 Test de sélection Client/Contrat...\n');

  const baseURL = 'http://localhost:3003';

  try {
    // Test 1: Récupérer tous les clients
    console.log('1️⃣ Test récupération des clients...');
    const clientsResponse = await axios.get(`${baseURL}/api/clients`);
    
    if (clientsResponse.data.success) {
      console.log(`✅ ${clientsResponse.data.count} clients récupérés`);
      
      if (clientsResponse.data.data.length > 0) {
        const firstClient = clientsResponse.data.data[0];
        console.log(`👤 Premier client: ${firstClient.nom} ${firstClient.prenom} (ID: ${firstClient.idclient})`);
        
        // Test 2: Récupérer tous les contrats
        console.log('\n2️⃣ Test récupération des contrats...');
        const contractsResponse = await axios.get(`${baseURL}/api/contracts`);
        
        if (contractsResponse.data.success) {
          console.log(`✅ ${contractsResponse.data.count} contrats récupérés`);
          
          // Filtrer les contrats pour le premier client
          const clientContracts = contractsResponse.data.data.filter(contract => 
            contract.idclient === firstClient.idclient
          );
          
          console.log(`📋 ${clientContracts.length} contrat(s) trouvé(s) pour ${firstClient.nom} ${firstClient.prenom}`);
          
          clientContracts.forEach((contract, index) => {
            console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
            console.log(`      Code QR: ${contract.codeqr || 'Non défini'}`);
            console.log(`      Marque: ${contract.marquecompteur || 'Non définie'}`);
          });
          
          // Test 3: API spécifique pour les contrats d'un client
          if (clientContracts.length > 0) {
            console.log(`\n3️⃣ Test API contrats du client ${firstClient.idclient}...`);
            try {
              const specificResponse = await axios.get(`${baseURL}/api/clients/${firstClient.idclient}/contracts`);
              
              if (specificResponse.data.success) {
                console.log(`✅ API spécifique: ${specificResponse.data.count} contrat(s) récupéré(s)`);
              } else {
                console.log('⚠️ API spécifique non disponible');
              }
            } catch (error) {
              console.log('⚠️ API spécifique non disponible:', error.message);
            }
          }
          
        } else {
          console.log('❌ Erreur récupération contrats');
        }
      } else {
        console.log('⚠️ Aucun client trouvé');
      }
    } else {
      console.log('❌ Erreur récupération clients');
    }

    console.log('\n🎉 Test terminé !');
    console.log('\n📋 Résumé:');
    console.log('✅ API clients fonctionnelle');
    console.log('✅ API contrats fonctionnelle');
    console.log('✅ Filtrage par client opérationnel');
    console.log('✅ Prêt pour la sélection dans le formulaire');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Vérifiez que le serveur consommation est démarré sur le port 3003');
    }
  }
}

testClientSelection();
