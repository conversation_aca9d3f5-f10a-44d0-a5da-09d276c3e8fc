const express = require('express');
const { pool } = require('./database');

const router = express.Router();

// Route pour scanner un QR code
router.get('/api/scan/:qrCode', async (req, res) => {
  try {
    const { qrCode } = req.params;
    console.log(`📥 Scan QR Code: ${qrCode}`);

    // Rechercher le client par QR code dans différentes tables possibles
    let clientFound = null;
    let searchMethod = '';

    // 1. Recherche directe par ID client si le QR code est un nombre
    if (!isNaN(qrCode)) {
      const clientQuery = `
        SELECT 
          c.*,
          s.nom as secteur_nom,
          s.ids as secteur_id
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        WHERE c.idclient = $1
      `;
      
      const clientResult = await pool.query(clientQuery, [parseInt(qrCode)]);
      
      if (clientResult.rows.length > 0) {
        clientFound = clientResult.rows[0];
        searchMethod = 'ID Client';
      }
    }

    // 2. Si pas trouvé, rechercher par référence de contrat
    if (!clientFound) {
      const contractQuery = `
        SELECT 
          c.*,
          s.nom as secteur_nom,
          s.ids as secteur_id,
          cont.reference as contrat_reference,
          cont.idcont
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        INNER JOIN contract cont ON c.idclient = cont.idclient
        WHERE cont.reference = $1
      `;
      
      const contractResult = await pool.query(contractQuery, [qrCode]);
      
      if (contractResult.rows.length > 0) {
        clientFound = contractResult.rows[0];
        searchMethod = 'Référence Contrat';
      }
    }

    // 3. Si pas trouvé, rechercher par numéro de téléphone
    if (!clientFound) {
      const phoneQuery = `
        SELECT 
          c.*,
          s.nom as secteur_nom,
          s.ids as secteur_id
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        WHERE c.tel = $1
      `;
      
      const phoneResult = await pool.query(phoneQuery, [qrCode]);
      
      if (phoneResult.rows.length > 0) {
        clientFound = phoneResult.rows[0];
        searchMethod = 'Numéro de téléphone';
      }
    }

    // 4. Si pas trouvé, rechercher par email
    if (!clientFound) {
      const emailQuery = `
        SELECT 
          c.*,
          s.nom as secteur_nom,
          s.ids as secteur_id
        FROM client c
        LEFT JOIN secteur s ON c.ids = s.ids
        WHERE c.email = $1
      `;
      
      const emailResult = await pool.query(emailQuery, [qrCode]);
      
      if (emailResult.rows.length > 0) {
        clientFound = emailResult.rows[0];
        searchMethod = 'Email';
      }
    }

    if (!clientFound) {
      console.log(`❌ Aucun client trouvé pour le QR code: ${qrCode}`);
      return res.status(404).json({
        success: false,
        message: `Aucun client trouvé pour le QR code: ${qrCode}`,
        qrCode: qrCode
      });
    }

    // Récupérer les contrats du client trouvé
    const contractsQuery = `
      SELECT 
        idcont,
        reference,
        datedebut,
        datefin,
        status
      FROM contract
      WHERE idclient = $1 AND status = 'active'
      ORDER BY datedebut DESC
    `;
    
    const contractsResult = await pool.query(contractsQuery, [clientFound.idclient]);

    // Récupérer la dernière consommation si des contrats existent
    let lastConsommation = null;
    if (contractsResult.rows.length > 0) {
      const lastConsQuery = `
        SELECT 
          consommationactuelle,
          periode,
          jours,
          status
        FROM consommation
        WHERE idcont = $1 AND status = 'active'
        ORDER BY periode DESC, idcons DESC
        LIMIT 1
      `;
      
      const lastConsResult = await pool.query(lastConsQuery, [contractsResult.rows[0].idcont]);
      if (lastConsResult.rows.length > 0) {
        lastConsommation = lastConsResult.rows[0];
      }
    }

    console.log(`✅ Client trouvé via ${searchMethod}:`, clientFound.nom, clientFound.prenom);
    
    res.json({
      success: true,
      data: {
        client: clientFound,
        contracts: contractsResult.rows,
        lastConsommation: lastConsommation,
        searchMethod: searchMethod
      },
      message: `Client trouvé via ${searchMethod}: ${clientFound.nom} ${clientFound.prenom}`,
      qrCode: qrCode
    });

  } catch (error) {
    console.error('❌ Erreur lors du scan du QR code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du scan du QR code',
      error: error.message,
      qrCode: req.params.qrCode
    });
  }
});

// Route pour générer un QR code pour un client
router.get('/api/generate-qr/:clientId', async (req, res) => {
  try {
    const { clientId } = req.params;
    console.log(`📥 Génération QR Code pour client: ${clientId}`);

    // Vérifier que le client existe
    const clientQuery = `
      SELECT 
        c.*,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;
    
    const clientResult = await pool.query(clientQuery, [clientId]);
    
    if (clientResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    const client = clientResult.rows[0];
    
    // Générer les données du QR code (peut être l'ID client ou une référence unique)
    const qrData = {
      type: 'client',
      id: client.idclient,
      nom: client.nom,
      prenom: client.prenom,
      secteur: client.secteur_nom,
      timestamp: new Date().toISOString()
    };

    console.log(`✅ QR Code généré pour: ${client.nom} ${client.prenom}`);
    
    res.json({
      success: true,
      data: {
        client: client,
        qrCode: client.idclient.toString(), // Simple: utiliser l'ID client comme QR code
        qrData: qrData,
        qrText: `Client: ${client.nom} ${client.prenom} (ID: ${client.idclient})`
      },
      message: `QR Code généré pour ${client.nom} ${client.prenom}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la génération du QR code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du QR code',
      error: error.message
    });
  }
});

// Route pour valider un QR code avant scan
router.post('/api/validate-qr', async (req, res) => {
  try {
    const { qrCode } = req.body;
    console.log(`📥 Validation QR Code: ${qrCode}`);

    if (!qrCode) {
      return res.status(400).json({
        success: false,
        message: 'QR Code requis'
      });
    }

    // Effectuer une recherche rapide pour valider
    let isValid = false;
    let clientPreview = null;

    // Recherche par ID client si c'est un nombre
    if (!isNaN(qrCode)) {
      const query = `
        SELECT idclient, nom, prenom
        FROM client
        WHERE idclient = $1
        LIMIT 1
      `;
      
      const result = await pool.query(query, [parseInt(qrCode)]);
      
      if (result.rows.length > 0) {
        isValid = true;
        clientPreview = result.rows[0];
      }
    }

    // Si pas trouvé par ID, rechercher par référence de contrat
    if (!isValid) {
      const query = `
        SELECT c.idclient, c.nom, c.prenom
        FROM client c
        INNER JOIN contract cont ON c.idclient = cont.idclient
        WHERE cont.reference = $1
        LIMIT 1
      `;
      
      const result = await pool.query(query, [qrCode]);
      
      if (result.rows.length > 0) {
        isValid = true;
        clientPreview = result.rows[0];
      }
    }

    res.json({
      success: true,
      data: {
        isValid: isValid,
        clientPreview: clientPreview,
        qrCode: qrCode
      },
      message: isValid ? 'QR Code valide' : 'QR Code non reconnu'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la validation du QR code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation du QR code',
      error: error.message
    });
  }
});

// Route pour obtenir l'historique des scans QR
router.get('/api/qr-history/:techId', async (req, res) => {
  try {
    const { techId } = req.params;
    console.log(`📥 Historique QR pour technicien: ${techId}`);

    // Pour l'instant, on peut utiliser l'historique des consommations comme proxy
    const query = `
      SELECT 
        cons.periode,
        cons.consommationactuelle,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.idclient,
        cont.reference as contrat_reference,
        cons.idcons
      FROM consommation cons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      WHERE cons.idtech = $1
      ORDER BY cons.periode DESC, cons.idcons DESC
      LIMIT 20
    `;

    const result = await pool.query(query, [techId]);

    console.log(`✅ ${result.rows.length} entrées d'historique trouvées`);
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} entrée(s) d'historique trouvée(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de l\'historique QR:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'historique QR',
      error: error.message
    });
  }
});

module.exports = router;
