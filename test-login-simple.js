// Test simple de la connexion
const axios = require('axios');

async function testLogin() {
  console.log('🧪 Test simple de connexion...');
  
  try {
    const response = await axios.post('http://localhost:3002/login', {
      email: '<EMAIL>',
      motDepass: 'Tech123'
    });
    
    console.log('✅ Connexion réussie !');
    console.log('Données utilisateur:', response.data.user);
    console.log('Rôle:', response.data.user.role);
    
    if (response.data.user.role === 'Tech') {
      console.log('🎯 Redirection vers TechnicianDashboard');
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    if (error.response) {
      console.log('Réponse serveur:', error.response.data);
    }
  }
}

testLogin();
