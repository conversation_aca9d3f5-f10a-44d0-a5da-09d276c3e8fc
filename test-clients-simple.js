const axios = require('axios');

async function testClients() {
  console.log('🧪 Test simple de l\'API clients...');
  
  try {
    // Test de connexion au serveur
    console.log('1. Test de connexion...');
    const testResponse = await axios.get('http://localhost:3003/');
    console.log('✅ Serveur accessible:', testResponse.data.message);
    
    // Test de récupération des clients
    console.log('2. Test récupération clients...');
    const clientsResponse = await axios.get('http://localhost:3003/api/clients');
    
    if (clientsResponse.data.success) {
      console.log('✅ Clients récupérés !');
      console.log(`📊 Nombre: ${clientsResponse.data.count}`);
      
      if (clientsResponse.data.data && clientsResponse.data.data.length > 0) {
        console.log('👥 Premier client:');
        const client = clientsResponse.data.data[0];
        console.log(`   - Nom: ${client.nom} ${client.prenom}`);
        console.log(`   - ID: ${client.idclient}`);
        console.log(`   - Adresse: ${client.adresse}`);
        console.log(`   - Ville: ${client.ville}`);
      }
    } else {
      console.log('❌ Erreur:', clientsResponse.data.message);
    }
    
  } catch (error) {
    console.log('❌ Erreur:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Le serveur clients n\'est pas démarré sur le port 3003');
    }
  }
}

testClients();
