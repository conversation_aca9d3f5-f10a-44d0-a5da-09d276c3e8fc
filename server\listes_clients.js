require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = process.env.CLIENTS_PORT || 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// ==================== FONCTIONS DE BASE DE DONNÉES ====================

// Test de connexion à la base de données
async function testConnection() {
  try {
    console.log('🔍 Test de connexion à la base de données Facutration...');
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données réussie !');

    // Test simple
    const result = await client.query('SELECT NOW()');
    console.log('⏰ Heure actuelle:', result.rows[0].now);

    client.release();
    return true;
  } catch (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
    return false;
  }
}

// Fonction pour récupérer tous les clients
async function getAllClients() {
  try {
    const client = await pool.connect();

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await client.query(query);
    client.release();

    console.log(`📋 ${result.rows.length} clients récupérés de la base de données`);
    return result.rows;
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des clients:', err.message);
    throw err;
  }
}

// Fonction pour récupérer un client par ID
async function getClientById(clientId) {
  try {
    const client = await pool.connect();

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;

    const result = await client.query(query, [clientId]);
    client.release();

    if (result.rows.length === 0) {
      return null;
    }

    console.log(`👤 Client ID ${clientId} récupéré`);
    return result.rows[0];
  } catch (err) {
    console.error('❌ Erreur lors de la récupération du client:', err.message);
    throw err;
  }
}

// Fonction pour récupérer les clients par secteur
async function getClientsBySecteur(secteurId) {
  try {
    const client = await pool.connect();

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.ids = $1
      ORDER BY c.nom, c.prenom
    `;

    const result = await client.query(query, [secteurId]);
    client.release();

    console.log(`🏘️ ${result.rows.length} clients du secteur ${secteurId} récupérés`);
    return result.rows;
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des clients par secteur:', err.message);
    throw err;
  }
}

// Fonction pour récupérer les statistiques des clients
async function getClientsStats() {
  try {
    const client = await pool.connect();

    const queries = {
      total: 'SELECT COUNT(*) as count FROM client',
      parSecteur: `
        SELECT
          s.nom as secteur,
          COUNT(c.idclient) as nombre_clients
        FROM secteur s
        LEFT JOIN client c ON s.ids = c.ids
        GROUP BY s.ids, s.nom
        ORDER BY nombre_clients DESC
      `
    };

    const totalResult = await client.query(queries.total);
    const secteurResult = await client.query(queries.parSecteur);

    client.release();

    const stats = {
      total: parseInt(totalResult.rows[0].count),
      parSecteur: secteurResult.rows
    };

    console.log(`📊 Statistiques clients: ${stats.total} total`);
    return stats;
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des statistiques:', err.message);
    throw err;
  }
}

// ==================== ROUTES API ====================

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur Liste Clients fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    const clients = await getAllClients();

    res.json({
      success: true,
      message: 'Clients récupérés avec succès',
      count: clients.length,
      data: clients
    });
  } catch (err) {
    console.error('Erreur API /api/clients:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: err.message
    });
  }
});

// Route pour récupérer un client par ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const clientId = parseInt(req.params.id);

    if (isNaN(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'ID client invalide'
      });
    }

    const client = await getClientById(clientId);

    if (!client) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    res.json({
      success: true,
      message: 'Client récupéré avec succès',
      data: client
    });
  } catch (err) {
    console.error('Erreur API /api/clients/:id:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: err.message
    });
  }
});

// Route pour récupérer les clients par secteur
app.get('/api/clients/secteur/:secteurId', async (req, res) => {
  try {
    const secteurId = parseInt(req.params.secteurId);

    if (isNaN(secteurId)) {
      return res.status(400).json({
        success: false,
        message: 'ID secteur invalide'
      });
    }

    const clients = await getClientsBySecteur(secteurId);

    res.json({
      success: true,
      message: 'Clients du secteur récupérés avec succès',
      secteurId: secteurId,
      count: clients.length,
      data: clients
    });
  } catch (err) {
    console.error('Erreur API /api/clients/secteur/:secteurId:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients par secteur',
      error: err.message
    });
  }
});

// Route pour récupérer les statistiques des clients
app.get('/api/clients/stats', async (req, res) => {
  try {
    const stats = await getClientsStats();

    res.json({
      success: true,
      message: 'Statistiques récupérées avec succès',
      data: stats
    });
  } catch (err) {
    console.error('Erreur API /api/clients/stats:', err);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: err.message
    });
  }
});

// ==================== DÉMARRAGE DU SERVEUR ====================
console.log('🚀 Démarrage du serveur Liste Clients...');

// Test de connexion au démarrage
testConnection().then(connected => {
  if (connected) {
    console.log('✅ Test de connexion à la base réussi');
  } else {
    console.log('⚠️ Problème de connexion à la base, mais le serveur démarre quand même');
  }
});

app.listen(PORT, () => {
  console.log(`✅ Serveur Liste Clients démarré sur http://localhost:${PORT}`);
  console.log(`📊 Base de données: Facutration`);
  console.log('\n📡 Routes disponibles:');
  console.log('  - GET  / (test)');
  console.log('  - GET  /api/clients (tous les clients)');
  console.log('  - GET  /api/clients/:id (client par ID)');
  console.log('  - GET  /api/clients/secteur/:secteurId (clients par secteur)');
  console.log('  - GET  /api/clients/stats (statistiques)');
  console.log('\n🎯 Le serveur est prêt à recevoir des requêtes !');
});

module.exports = app;