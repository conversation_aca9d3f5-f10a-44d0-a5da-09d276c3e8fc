import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const SaisieClientPage = ({ onBack }) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    adresse: '',
    ville: '',
    tel: '',
    email: '',
    ids: '' // secteur
  });
  
  const [secteurs, setSecteurs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' ou 'error'
  const [qrCodeImage, setQrCodeImage] = useState(''); // Pour afficher le QR code généré
  const [clientData, setClientData] = useState(null); // Données du client créé
  const [generatedQR, setGeneratedQR] = useState(null); // QR Code généré

  const API_BASE_URL = 'http://localhost:3002'; // Utiliser le bon port pour notre API

  // Charger les secteurs au démarrage
  useEffect(() => {
    fetchSecteurs();
  }, []);

  const fetchSecteurs = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/table/secteur`);
      const data = await response.json();
      if (data.success) {
        setSecteurs(data.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des secteurs:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    // Validation des champs obligatoires
    if (!formData.nom || !formData.prenom || !formData.adresse) {
      setMessage('Les champs Nom, Prénom et Adresse sont obligatoires');
      setMessageType('error');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/clients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        // Afficher le message de succès avec le QR Code
        setMessage(`Client ${formData.prenom} ${formData.nom} ajouté avec succès ! QR Code: ${data.data.qrCode}`);
        setMessageType('success');

        // Sauvegarder les données du client et du contrat
        setClientData(data.data.client);
        setGeneratedQR(data.data);

        // Afficher le QR code généré
        if (data.data.qrCode) {
          setQrCodeImage(data.data.qrCode);
        }

        // Réinitialiser le formulaire après un délai pour voir le QR Code
        setTimeout(() => {
          setFormData({
            nom: '',
            prenom: '',
            adresse: '',
            ville: '',
            tel: '',
            email: '',
            ids: ''
          });
          setMessage('');
          setQrCodeImage('');
          setClientData(null);
          setGeneratedQR(null);
        }, 5000); // 5 secondes pour voir le QR Code

      } else {
        setMessage(data.message || 'Erreur lors de l\'ajout du client');
        setMessageType('error');
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du client:', error);
      setMessage('Erreur de connexion au serveur');
      setMessageType('error');
    } finally {
      setLoading(false);
    }
  };

  const clearMessage = () => {
    setMessage('');
    setMessageType('');
    setQrCodeImage('');
    setClientData(null);
    setGeneratedQR(null);
  };

  return (
    <div className="tech-mobile-container">
      {/* Header */}
      <div className="tech-mobile-header">
        <button onClick={onBack} className="tech-back-button">
          ←
        </button>
        <div>
          <h1 className="tech-mobile-card-title">Saisie Client</h1>
          <p className="tech-mobile-card-subtitle">Ajouter un nouveau client</p>
        </div>
      </div>

      {/* Message de retour */}
      {message && (
        <div className={`tech-mobile-card ${messageType === 'success' ? 'success-message' : 'error-message'}`}
             style={{
               marginBottom: '20px',
               backgroundColor: messageType === 'success' ? '#d4edda' : '#f8d7da',
               borderColor: messageType === 'success' ? '#c3e6cb' : '#f5c6cb',
               color: messageType === 'success' ? '#155724' : '#721c24'
             }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{message}</span>
            <button
              onClick={clearMessage}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '18px',
                cursor: 'pointer',
                color: messageType === 'success' ? '#155724' : '#721c24'
              }}
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Affichage du QR Code généré */}
      {generatedQR && messageType === 'success' && (
        <div className="tech-mobile-card" style={{
          marginBottom: '20px',
          backgroundColor: '#e7f3ff',
          borderColor: '#007bff',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#007bff', marginBottom: '15px' }}>
            🎉 QR Code Généré avec Succès !
          </h3>
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '15px',
            borderRadius: '8px',
            marginBottom: '15px'
          }}>
            <p><strong>Client:</strong> {generatedQR.client.nom} {generatedQR.client.prenom}</p>
            <p><strong>QR Code:</strong> <code style={{
              backgroundColor: '#fff',
              padding: '4px 8px',
              borderRadius: '4px',
              color: '#e83e8c',
              fontWeight: 'bold'
            }}>{generatedQR.qrCode}</code></p>
            <p><strong>ID Contrat:</strong> {generatedQR.contract.idcontract}</p>
            <p><strong>Date:</strong> {new Date(generatedQR.contract.datecontract).toLocaleDateString()}</p>
          </div>
          <p style={{ fontSize: '14px', color: '#6c757d' }}>
            ✅ Le QR Code a été enregistré dans la table Contract de votre base de données
          </p>
        </div>
      )}

      {/* Formulaire de saisie */}
      <div className="tech-mobile-card">
        <form onSubmit={handleSubmit}>
          {/* Nom et Prénom */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
            <div className="tech-form-group">
              <label className="tech-form-label">
                Nom <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                name="nom"
                value={formData.nom}
                onChange={handleInputChange}
                className="tech-form-input"
                placeholder="Nom du client"
                required
              />
            </div>
            <div className="tech-form-group">
              <label className="tech-form-label">
                Prénom <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                name="prenom"
                value={formData.prenom}
                onChange={handleInputChange}
                className="tech-form-input"
                placeholder="Prénom du client"
                required
              />
            </div>
          </div>

          {/* Adresse */}
          <div className="tech-form-group" style={{ marginBottom: '20px' }}>
            <label className="tech-form-label">Adresse</label>
            <textarea
              name="adresse"
              value={formData.adresse}
              onChange={handleInputChange}
              className="tech-form-input"
              placeholder="Adresse complète du client"
              rows="3"
              style={{ resize: 'vertical', minHeight: '80px' }}
            />
          </div>

          {/* Ville et Secteur */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
            <div className="tech-form-group">
              <label className="tech-form-label">
                Ville <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                name="ville"
                value={formData.ville}
                onChange={handleInputChange}
                className="tech-form-input"
                placeholder="Ville"
                required
              />
            </div>
            <div className="tech-form-group">
              <label className="tech-form-label">Secteur</label>
              <select
                name="ids"
                value={formData.ids}
                onChange={handleInputChange}
                className="tech-form-input"
              >
                <option value="">Sélectionner un secteur</option>
                {secteurs.map(secteur => (
                  <option key={secteur.ids} value={secteur.ids}>
                    {secteur.nom}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Téléphone et Email */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '30px' }}>
            <div className="tech-form-group">
              <label className="tech-form-label">Téléphone</label>
              <input
                type="tel"
                name="tel"
                value={formData.tel}
                onChange={handleInputChange}
                className="tech-form-input"
                placeholder="Numéro de téléphone"
              />
            </div>
            <div className="tech-form-group">
              <label className="tech-form-label">Email</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="tech-form-input"
                placeholder="Adresse email"
              />
            </div>
          </div>

          {/* Boutons d'action */}
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <button
              type="button"
              onClick={onBack}
              className="tech-mobile-action-btn"
              style={{ 
                backgroundColor: '#6b7280',
                flex: '1'
              }}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading}
              className="tech-mobile-action-btn"
              style={{ 
                backgroundColor: loading ? '#9ca3af' : '#3b82f6',
                flex: '1'
              }}
            >
              {loading ? 'Enregistrement...' : 'Enregistrer Client'}
            </button>
          </div>
        </form>
      </div>

      {/* Affichage du QR Code généré */}
      {qrCodeImage && clientData && (
        <div className="tech-mobile-card" style={{ marginTop: '20px', textAlign: 'center' }}>
          <h3 style={{ color: '#059669', marginBottom: '15px' }}>
            🎉 Code QR généré pour {clientData.prenom} {clientData.nom}
          </h3>

          <div style={{
            backgroundColor: '#ffffff',
            padding: '20px',
            borderRadius: '10px',
            border: '2px solid #059669',
            display: 'inline-block'
          }}>
            <img
              src={qrCodeImage}
              alt="Code QR du client"
              style={{
                width: '200px',
                height: '200px',
                border: '1px solid #e5e7eb'
              }}
            />
          </div>

          <div style={{ marginTop: '15px' }}>
            <p style={{ margin: '5px 0', fontSize: '14px', color: '#374151' }}>
              <strong>Code QR:</strong> {clientData.codeqr}
            </p>
            <p style={{ margin: '5px 0', fontSize: '14px', color: '#374151' }}>
              <strong>ID Client:</strong> {clientData.idclient}
            </p>
          </div>

          <button
            onClick={clearMessage}
            className="tech-mobile-action-btn"
            style={{
              backgroundColor: '#059669',
              marginTop: '15px',
              padding: '10px 20px'
            }}
          >
            Ajouter un autre client
          </button>
        </div>
      )}

      {/* Informations sur les champs obligatoires */}
      <div className="tech-mobile-card" style={{ marginTop: '20px', backgroundColor: '#f8f9fa' }}>
        <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
          <span style={{ color: 'red' }}>*</span> Champs obligatoires
        </p>
      </div>
    </div>
  );
};

export default SaisieClientPage;
