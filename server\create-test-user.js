const { Pool } = require('pg');

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',        // Votre nom d'utilisateur PostgreSQL
  host: 'localhost',       // Adresse du serveur PostgreSQL
  database: 'Facutration', // Nom de votre base de données (avec 'u')
  password: '123456',      // Votre mot de passe PostgreSQL
  port: 5432,             // Port PostgreSQL par défaut
});

async function createTestUsers() {
  console.log('👤 Création d\'utilisateurs de test dans la table Utilisateur...\n');

  try {
    const client = await pool.connect();
    console.log('✅ Connexion réussie à la base "Facutration"\n');

    // Vérifier si la table existe
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'utilisateur'
      );
    `;
    
    const tableExistsResult = await client.query(tableExistsQuery);
    const tableExists = tableExistsResult.rows[0].exists;
    
    if (!tableExists) {
      console.log('❌ Table "utilisateur" non trouvée');
      console.log('💡 Créez d\'abord la table avec le script SQL fourni');
      client.release();
      return;
    }

    // Utilisateurs de test à créer
    const testUsers = [
      {
        nom: 'Technicien',
        prenom: 'Test',
        adresse: '123 Rue de la Technique',
        tel: '0123456789',
        email: '<EMAIL>',
        motdepass: 'Tech123',
        role: 'Tech'
      },
      {
        nom: 'Admin',
        prenom: 'Principal',
        adresse: '456 Avenue de l\'Administration',
        tel: '0987654321',
        email: '<EMAIL>',
        motdepass: 'Admin123',
        role: 'Admin'
      },
      {
        nom: 'Dupont',
        prenom: 'Jean',
        adresse: '789 Boulevard des Techniciens',
        tel: '0555123456',
        email: '<EMAIL>',
        motdepass: 'Jean123',
        role: 'Tech'
      }
    ];

    console.log('🔍 Vérification des utilisateurs existants...');

    for (const user of testUsers) {
      // Vérifier si l'utilisateur existe déjà
      const checkQuery = 'SELECT email FROM utilisateur WHERE email = $1';
      const checkResult = await client.query(checkQuery, [user.email]);

      if (checkResult.rows.length > 0) {
        console.log(`⚠️  Utilisateur ${user.email} existe déjà - ignoré`);
      } else {
        // Insérer le nouvel utilisateur
        const insertQuery = `
          INSERT INTO utilisateur (nom, prenom, adresse, tel, email, motdepass, role)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING idtech, nom, prenom, email, role
        `;
        
        const insertResult = await client.query(insertQuery, [
          user.nom,
          user.prenom,
          user.adresse,
          user.tel,
          user.email,
          user.motdepass,
          user.role
        ]);

        const newUser = insertResult.rows[0];
        console.log(`✅ Utilisateur créé: ${newUser.nom} ${newUser.prenom} (${newUser.email}) - Rôle: ${newUser.role} - ID: ${newUser.idtech}`);
      }
    }

    console.log('\n📊 Résumé des utilisateurs dans la table:');
    const summaryQuery = `
      SELECT idtech, nom, prenom, email, role 
      FROM utilisateur 
      ORDER BY role, nom
    `;
    const summaryResult = await client.query(summaryQuery);
    
    summaryResult.rows.forEach((user, index) => {
      console.log(`   ${index + 1}. [${user.role}] ${user.nom} ${user.prenom} (${user.email}) - ID: ${user.idtech}`);
    });

    console.log('\n🎯 Comptes de test disponibles pour la connexion:');
    console.log('   👨‍🔧 Technicien: <EMAIL> / Tech123');
    console.log('   👨‍💼 Admin: <EMAIL> / Admin123');
    console.log('   👨‍🔧 Jean Dupont: <EMAIL> / Jean123');

    client.release();
    console.log('\n🎉 Création des utilisateurs de test terminée !');

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs:', error.message);
    console.error('📋 Détails:', error);
    
    if (error.code === '23505') {
      console.log('💡 Erreur de contrainte unique - un utilisateur avec cet email existe déjà');
    } else if (error.code === '42P01') {
      console.log('💡 Table non trouvée - créez d\'abord la table Utilisateur');
    }
  } finally {
    await pool.end();
  }
}

// Exécuter la création
createTestUsers();
