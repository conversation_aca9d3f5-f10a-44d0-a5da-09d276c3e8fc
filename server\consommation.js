const express = require('express');
const { pool } = require('./database');

const router = express.Router();

// Route pour ajouter une nouvelle consommation
router.post('/api/consommation', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/consommation:', req.body);
    const {
      consommationpre,
      consommationactuelle,
      idcont,
      idtech,
      idtranch,
      jours,
      periode,
      status = 'active'
    } = req.body;

    // Validation des champs requis
    if (!consommationactuelle || !idcont || !idtech || !periode) {
      return res.status(400).json({
        success: false,
        message: 'Les champs consommationactuelle, idcont, idtech et periode sont requis'
      });
    }

    // Validation: consommation actuelle doit être supérieure à la précédente
    if (consommationpre && consommationactuelle <= consommationpre) {
      return res.status(400).json({
        success: false,
        message: 'La consommation actuelle doit être supérieure à la consommation précédente'
      });
    }

    const query = `
      INSERT INTO consommation (consommationpre, consommationactuelle, idcont, idtech, idtranch, jours, periode, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [consommationpre, consommationactuelle, idcont, idtech, idtranch, jours, periode, status];
    const result = await pool.query(query, values);

    console.log('✅ Nouvelle consommation enregistrée:', result.rows[0]);
    res.status(201).json({
      success: true,
      data: result.rows[0],
      message: 'Consommation enregistrée avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement de la consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement de la consommation',
      error: error.message
    });
  }
});

// Route pour récupérer toutes les consommations
router.get('/api/consommation', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/consommation');

    const query = `
      SELECT 
        cons.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        cont.reference as contrat_reference,
        u.nom as technicien_nom,
        u.prenom as technicien_prenom
      FROM consommation cons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN utilisateur u ON cons.idtech = u.idtech
      ORDER BY cons.periode DESC, cons.idcons DESC
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} consommations récupérées`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} consommation(s) trouvée(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des consommations:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des consommations',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats avec informations client (pour le formulaire de consommation)
router.get('/api/contracts', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/contracts');

    const query = `
      SELECT
        cont.idcont,
        cont.reference,
        cont.datedebut,
        cont.datefin,
        cont.status as contrat_status,
        c.idclient,
        c.nom as client_nom,
        c.prenom as client_prenom,
        c.adresse as client_adresse,
        c.ville as client_ville,
        s.nom as secteur_nom
      FROM contract cont
      INNER JOIN client c ON cont.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE cont.status = 'active'
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} contrats récupérés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} contrat(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour obtenir la dernière consommation d'un contrat spécifique
router.get('/api/contracts/:idcontract/last-consommation', async (req, res) => {
  try {
    const { idcontract } = req.params;
    console.log(`📥 Requête GET /api/contracts/${idcontract}/last-consommation`);

    const query = `
      SELECT 
        consommationactuelle,
        periode,
        jours,
        status
      FROM consommation
      WHERE idcont = $1 AND status = 'active'
      ORDER BY periode DESC, idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [idcontract]);

    if (result.rows.length === 0) {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${idcontract}`);
      return res.json({
        success: true,
        data: null,
        message: 'Aucune consommation précédente trouvée pour ce contrat'
      });
    }

    console.log(`✅ Dernière consommation trouvée pour le contrat ${idcontract}:`, result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Dernière consommation trouvée'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route pour obtenir la toute dernière consommation globale de la base
router.get('/api/last-consommation-global', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/last-consommation-global');

    const query = `
      SELECT 
        cons.consommationactuelle,
        cons.periode,
        cons.jours,
        cons.status,
        c.nom as client_nom,
        c.prenom as client_prenom,
        cont.reference as contrat_reference
      FROM consommation cons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      WHERE cons.status = 'active'
      ORDER BY cons.periode DESC, cons.idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(query);

    if (result.rows.length === 0) {
      console.log('ℹ️ Aucune consommation trouvée dans la base');
      return res.json({
        success: true,
        data: null,
        message: 'Aucune consommation trouvée dans la base de données'
      });
    }

    console.log('✅ Dernière consommation globale trouvée:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Dernière consommation globale trouvée'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation globale:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation globale',
      error: error.message
    });
  }
});

// Route pour récupérer les consommations d'un technicien spécifique
router.get('/api/consommation/technicien/:idtech', async (req, res) => {
  try {
    const { idtech } = req.params;
    console.log(`📥 Requête GET /api/consommation/technicien/${idtech}`);

    const query = `
      SELECT 
        cons.*,
        c.nom as client_nom,
        c.prenom as client_prenom,
        cont.reference as contrat_reference
      FROM consommation cons
      LEFT JOIN contract cont ON cons.idcont = cont.idcont
      LEFT JOIN client c ON cont.idclient = c.idclient
      WHERE cons.idtech = $1
      ORDER BY cons.periode DESC, cons.idcons DESC
    `;

    const result = await pool.query(query, [idtech]);

    console.log(`✅ ${result.rows.length} consommations trouvées pour le technicien ${idtech}`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} consommation(s) trouvée(s) pour ce technicien`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des consommations du technicien:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des consommations du technicien',
      error: error.message
    });
  }
});

module.exports = router;
