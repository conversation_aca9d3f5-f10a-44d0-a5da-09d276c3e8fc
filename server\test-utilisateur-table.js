const { Pool } = require('pg');

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',        // Votre nom d'utilisateur PostgreSQL
  host: 'localhost',       // Adresse du serveur PostgreSQL
  database: 'Facutration', // Nom de votre base de données (avec 'u')
  password: '123456',      // Votre mot de passe PostgreSQL
  port: 5432,             // Port PostgreSQL par défaut
});

async function testUtilisateurTable() {
  console.log('🔍 Test de la table Utilisateur dans la base "Facutration"...\n');

  try {
    // Test de connexion
    console.log('1️⃣ Test de connexion à la base de données...');
    const client = await pool.connect();
    console.log('✅ Connexion réussie à la base "Facutration"\n');

    // Vérifier si la table Utilisateur existe
    console.log('2️⃣ Vérification de l\'existence de la table Utilisateur...');
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'utilisateur'
      );
    `;
    
    const tableExistsResult = await client.query(tableExistsQuery);
    const tableExists = tableExistsResult.rows[0].exists;
    
    if (tableExists) {
      console.log('✅ Table "utilisateur" trouvée\n');
    } else {
      console.log('❌ Table "utilisateur" non trouvée');
      console.log('💡 Vérifiez que la table a été créée avec le bon nom\n');
      
      // Lister toutes les tables disponibles
      console.log('📋 Tables disponibles dans la base "Facutration":');
      const tablesQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
      `;
      const tablesResult = await client.query(tablesQuery);
      tablesResult.rows.forEach(row => {
        console.log(`   - ${row.table_name}`);
      });
      
      client.release();
      return;
    }

    // Vérifier la structure de la table
    console.log('3️⃣ Vérification de la structure de la table Utilisateur...');
    const structureQuery = `
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'utilisateur'
      ORDER BY ordinal_position;
    `;
    
    const structureResult = await client.query(structureQuery);
    console.log('📋 Structure de la table "utilisateur":');
    structureResult.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    console.log('');

    // Compter le nombre d'utilisateurs
    console.log('4️⃣ Comptage des utilisateurs...');
    const countQuery = 'SELECT COUNT(*) as total FROM utilisateur';
    const countResult = await client.query(countQuery);
    const totalUsers = countResult.rows[0].total;
    console.log(`📊 Nombre total d'utilisateurs: ${totalUsers}\n`);

    if (totalUsers > 0) {
      // Afficher quelques exemples d'utilisateurs
      console.log('5️⃣ Exemples d\'utilisateurs dans la table:');
      const usersQuery = `
        SELECT idtech, nom, prenom, email, role 
        FROM utilisateur 
        ORDER BY idtech 
        LIMIT 5
      `;
      const usersResult = await client.query(usersQuery);
      
      usersResult.rows.forEach((user, index) => {
        console.log(`   ${index + 1}. ID: ${user.idtech}, Nom: ${user.nom} ${user.prenom}, Email: ${user.email}, Rôle: ${user.role}`);
      });
      console.log('');

      // Vérifier s'il y a des utilisateurs avec le rôle "Tech"
      console.log('6️⃣ Recherche d\'utilisateurs avec le rôle "Tech"...');
      const techUsersQuery = `
        SELECT idtech, nom, prenom, email, role 
        FROM utilisateur 
        WHERE role = 'Tech'
        ORDER BY idtech
      `;
      const techUsersResult = await client.query(techUsersQuery);
      
      if (techUsersResult.rows.length > 0) {
        console.log(`✅ ${techUsersResult.rows.length} utilisateur(s) avec le rôle "Tech" trouvé(s):`);
        techUsersResult.rows.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.nom} ${user.prenom} (${user.email})`);
        });
      } else {
        console.log('❌ Aucun utilisateur avec le rôle "Tech" trouvé');
        console.log('💡 Vous devez ajouter au moins un utilisateur avec role = "Tech"');
      }
      console.log('');

      // Test de connexion avec un utilisateur spécifique
      console.log('7️⃣ Test de requête de connexion...');
      const loginTestQuery = `
        SELECT idtech, nom, prenom, adresse, tel, email, role
        FROM utilisateur
        WHERE email = $1 AND motdepass = $2
      `;
      
      // Test avec les identifiants par défaut
      const testEmail = '<EMAIL>';
      const testPassword = 'Tech123';
      
      console.log(`🔍 Test avec email: ${testEmail} et mot de passe: ${testPassword}`);
      const loginTestResult = await client.query(loginTestQuery, [testEmail, testPassword]);
      
      if (loginTestResult.rows.length > 0) {
        const user = loginTestResult.rows[0];
        console.log('✅ Utilisateur de test trouvé:');
        console.log(`   - ID: ${user.idtech}`);
        console.log(`   - Nom: ${user.nom} ${user.prenom}`);
        console.log(`   - Email: ${user.email}`);
        console.log(`   - Rôle: ${user.role}`);
        console.log(`   - Adresse: ${user.adresse}`);
        console.log(`   - Téléphone: ${user.tel}`);
      } else {
        console.log('❌ Aucun utilisateur trouvé avec ces identifiants');
        console.log('💡 Ajoutez un utilisateur de test avec ces identifiants:');
        console.log(`   INSERT INTO utilisateur (nom, prenom, adresse, tel, email, motdepass, role)`);
        console.log(`   VALUES ('Technicien', 'Test', '123 Rue Test', '0123456789', '${testEmail}', '${testPassword}', 'Tech');`);
      }

    } else {
      console.log('❌ La table "utilisateur" est vide');
      console.log('💡 Ajoutez des utilisateurs à la table pour tester la connexion');
    }

    client.release();
    console.log('\n🎉 Test terminé avec succès !');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    console.error('📋 Détails:', error);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testUtilisateurTable();
