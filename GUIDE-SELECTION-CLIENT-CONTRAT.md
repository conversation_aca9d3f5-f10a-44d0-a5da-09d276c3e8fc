# 🎯 Guide de Sélection Client et Contrat

## ✅ **SYSTÈME CONFIGURÉ !**

Le système utilise maintenant **100% vos tables `Client` et `Contract`** pour afficher automatiquement les clients et leurs contrats lors de la sélection.

## 🔧 **Modifications Effectuées**

### 1. **Backend - Serveur de Consommation**
- **Fichier modifié** : `server/consommation-server.js`
- **Nouvelles routes ajoutées** :
  - `GET /api/clients` - Récupère tous les clients depuis la table `Client`
  - `GET /api/contracts` - Récupère tous les contrats depuis la table `Contract`
  - `GET /api/clients/:clientId/contracts` - Récupère les contrats d'un client spécifique

### 2. **Frontend - Page de Consommation**
- **Fichier modifié** : `src/pages/ConsommationPage.js`
- **Fonction améliorée** : `handleClientChange()` récupère automatiquement les contrats du client sélectionné

## 📊 **Structure des Tables Utilisées**

### **Table Client**
```sql
CREATE TABLE Client (
    idClient SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    ville VARCHAR(100),
    tel VARCHAR(20),
    email VARCHAR(100),
    idS INT REFERENCES Secteur(idS)
);
```

### **Table Contract**
```sql
CREATE TABLE Contract (
    idContract SERIAL PRIMARY KEY,
    codeQr VARCHAR(100),
    dateContract TIMESTAMP,
    idClient INT REFERENCES Client(idClient),
    marqueCompteur VARCHAR(100),
    numSerieCompteur VARCHAR(100),
    posX VARCHAR(50),
    posY VARCHAR(50)
);
```

## 🚀 **Fonctionnement du Système**

### **1. Chargement Initial**
```
📋 Chargement des clients depuis la table Client
📋 Chargement des contrats depuis la table Contract
🔗 Jointure avec la table Secteur pour les noms de secteurs
```

### **2. Sélection d'un Client**
```
👤 Utilisateur sélectionne un client dans la liste
🔍 Système filtre automatiquement les contrats de ce client
📋 Affichage des contrats disponibles pour ce client
✅ Prêt pour la saisie de consommation
```

### **3. Requête SQL Exécutée**
```sql
SELECT 
  co.idcontract,
  co.codeqr,
  co.datecontract,
  co.marquecompteur,
  co.numseriecompteur,
  c.idclient,
  c.nom,
  c.prenom,
  c.adresse,
  c.ville,
  c.tel,
  c.email,
  s.nom as secteur_nom
FROM contract co
LEFT JOIN client c ON co.idclient = c.idclient
LEFT JOIN secteur s ON c.ids = s.ids
WHERE co.idclient = $1
ORDER BY co.idcontract
```

## 📱 **Interface Utilisateur**

### **Formulaire de Consommation**
```
┌─────────────────────────────────────────────────────────┐
│ 📅 Période (YYYY-MM) *     [2025-07]                   │
│                                                         │
│ 👤 Client *                                             │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [Sélectionner un client]                            │ │
│ │ Benali Fatima - Sefrou                              │ │
│ │ Bennani Fatima - Fès                                │ │
│ │ El Amrani Ahmed - Fès                               │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│ 📋 Contrat *                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ [Contrats du client sélectionné]                    │ │
│ │ Contrat #1 - QR123 (Sagemcom)                      │ │
│ │ Contrat #2 - QR456 (Sensus)                        │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 🔄 **Flux de Données**

### **Étape 1 : Sélection du Client**
1. **Utilisateur clique** sur un client dans la liste
2. **Frontend appelle** `handleClientChange(clientId)`
3. **API appelée** : `GET /api/contracts` 
4. **Filtrage** des contrats pour ce client
5. **Mise à jour** de la liste des contrats disponibles

### **Étape 2 : Affichage des Contrats**
1. **Contrats filtrés** par `idClient`
2. **Affichage** dans le dropdown "Contrat *"
3. **Format** : "Contrat #ID - CodeQR (Marque)"
4. **Prêt** pour sélection

## 📋 **APIs Disponibles**

### **GET /api/clients**
Récupère tous les clients avec leurs secteurs
```json
{
  "success": true,
  "message": "Clients récupérés depuis la table Client",
  "count": 9,
  "data": [
    {
      "idclient": 3,
      "nom": "Benali",
      "prenom": "Fatima",
      "ville": "Sefrou",
      "secteur_nom": "Résidentiel"
    }
  ]
}
```

### **GET /api/contracts**
Récupère tous les contrats avec les informations clients
```json
{
  "success": true,
  "message": "Contrats récupérés depuis les tables Client et Contract",
  "count": 5,
  "data": [
    {
      "idcontract": 1,
      "codeqr": "QR123",
      "marquecompteur": "Sagemcom",
      "idclient": 3,
      "nom": "Benali",
      "prenom": "Fatima"
    }
  ]
}
```

### **GET /api/clients/:clientId/contracts**
Récupère les contrats d'un client spécifique
```json
{
  "success": true,
  "message": "Contrats du client 3 récupérés avec succès",
  "clientId": 3,
  "count": 2,
  "data": [...]
}
```

## 🎯 **Test du Système**

### **Pour tester :**

1. **Démarrez le serveur** :
   ```bash
   node server/consommation-server.js
   ```

2. **Accédez au formulaire** :
   - Connectez-vous avec `<EMAIL>` / `Tech123`
   - Cliquez sur "CONSOMMATION"

3. **Testez la sélection** :
   - Sélectionnez un client dans la liste
   - Vérifiez que les contrats se chargent automatiquement
   - Consultez la console pour voir les logs détaillés

## 🎉 **Résultat Final**

✅ **Utilisation 100% de vos tables `Client` et `Contract`**
✅ **Sélection automatique des contrats par client**
✅ **Jointures avec la table `Secteur`**
✅ **Interface intuitive et responsive**
✅ **Logs détaillés pour le débogage**

---

**🎯 Le système de sélection Client/Contrat utilise maintenant entièrement vos tables de base de données !**
