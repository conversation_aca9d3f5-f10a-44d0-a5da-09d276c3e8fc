const axios = require('axios');

async function testLoginAPI() {
  console.log('🧪 Test de l\'API de login avec la base de données "Facutration"...\n');

  const baseURL = 'http://localhost:3002';

  try {
    // Test 1: Vérifier que le serveur répond
    console.log('1️⃣ Test de connexion au serveur...');
    try {
      const response = await axios.get(`${baseURL}/`);
      console.log('✅ Serveur accessible');
      console.log(`📡 Réponse: ${response.data.message}`);
      console.log(`⏰ Timestamp: ${response.data.timestamp}\n`);
    } catch (error) {
      console.log('❌ Serveur non accessible');
      console.log('💡 Vérifiez que le serveur login-server.js est démarré sur le port 3002\n');
      return;
    }

    // Test 2: Login avec utilisateur Tech valide
    console.log('2️⃣ Test de login avec utilisateur Tech (base de données)...');
    try {
      const loginData = {
        email: '<EMAIL>',
        motDepass: 'Tech123'
      };

      console.log(`📧 Email: ${loginData.email}`);
      console.log(`🔑 Mot de passe: ${loginData.motDepass}`);

      const response = await axios.post(`${baseURL}/login`, loginData);
      
      if (response.data.success) {
        console.log('✅ Login réussi !');
        console.log('👤 Données utilisateur reçues:');
        console.log(`   - ID: ${response.data.user.idtech}`);
        console.log(`   - Nom: ${response.data.user.nom} ${response.data.user.prenom}`);
        console.log(`   - Email: ${response.data.user.email}`);
        console.log(`   - Rôle: ${response.data.user.role}`);
        console.log(`   - Adresse: ${response.data.user.adresse || 'Non renseignée'}`);
        console.log(`   - Téléphone: ${response.data.user.tel || 'Non renseigné'}`);
        
        // Vérifier la redirection selon le rôle
        if (response.data.user.role === 'Tech') {
          console.log('🎯 ✅ Redirection correcte vers TechnicianDashboard (rôle Tech détecté)');
        } else {
          console.log('🎯 ⚠️  Redirection vers Dashboard Admin (rôle non-Tech)');
        }
      } else {
        console.log('❌ Login échoué');
        console.log(`📝 Message: ${response.data.message}`);
      }
    } catch (error) {
      console.log('❌ Erreur lors du login');
      console.log(`📝 Message: ${error.response?.data?.message || error.message}`);
    }
    console.log('');

    // Test 3: Login avec utilisateur Admin
    console.log('3️⃣ Test de login avec utilisateur Admin...');
    try {
      const adminLoginData = {
        email: '<EMAIL>',
        motDepass: 'Admin123'
      };

      const response = await axios.post(`${baseURL}/login`, adminLoginData);
      
      if (response.data.success) {
        console.log('✅ Login Admin réussi !');
        console.log(`👤 Utilisateur: ${response.data.user.nom} ${response.data.user.prenom}`);
        console.log(`🎯 Rôle: ${response.data.user.role}`);
        
        if (response.data.user.role === 'Admin') {
          console.log('🎯 ✅ Redirection correcte vers Dashboard Admin');
        }
      } else {
        console.log('❌ Login Admin échoué');
        console.log(`📝 Message: ${response.data.message}`);
      }
    } catch (error) {
      console.log('❌ Erreur lors du login Admin');
      console.log(`📝 Message: ${error.response?.data?.message || error.message}`);
    }
    console.log('');

    // Test 4: Login avec identifiants incorrects
    console.log('4️⃣ Test avec identifiants incorrects...');
    try {
      const wrongLoginData = {
        email: '<EMAIL>',
        motDepass: 'wrongpassword'
      };

      const response = await axios.post(`${baseURL}/login`, wrongLoginData);
      console.log('⚠️  Réponse inattendue pour identifiants incorrects');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Rejet correct des identifiants incorrects');
        console.log(`📝 Message: ${error.response.data.message}`);
      } else {
        console.log('❌ Erreur inattendue');
        console.log(`📝 Message: ${error.response?.data?.message || error.message}`);
      }
    }
    console.log('');

    // Test 5: Login sans données
    console.log('5️⃣ Test sans données de login...');
    try {
      const response = await axios.post(`${baseURL}/login`, {});
      console.log('⚠️  Réponse inattendue pour données manquantes');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Validation correcte des champs requis');
        console.log(`📝 Message: ${error.response.data.message}`);
      } else {
        console.log('❌ Erreur inattendue');
        console.log(`📝 Message: ${error.response?.data?.message || error.message}`);
      }
    }

    console.log('\n🎉 Tests terminés !');
    console.log('\n📋 Résumé:');
    console.log('✅ Le serveur utilise bien la base de données "Facutration"');
    console.log('✅ La table Utilisateur est correctement consultée');
    console.log('✅ La redirection selon le rôle fonctionne');
    console.log('✅ Les utilisateurs avec rôle "Tech" sont dirigés vers TechnicianDashboard');

  } catch (error) {
    console.error('❌ Erreur générale lors des tests:', error.message);
  }
}

// Exécuter les tests
testLoginAPI();
