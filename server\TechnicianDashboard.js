require('dotenv').config();
const express = require('express');
const cors = require('cors');

// Import des modules spécialisés
const { pool, detectDatabaseTables, getTableCounts, initializeDatabase } = require('./database');
const clientsRoutes = require('./clients');
const consommationRoutes = require('./consommation');
const facturesRoutes = require('./factures');
const codeQRRoutes = require('./codeQR');
const authRoutes = require('./auth');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Utilisation des routes modulaires
app.use('/', clientsRoutes);
app.use('/', consommationRoutes);
app.use('/', facturesRoutes);
app.use('/', codeQRRoutes);
app.use('/', authRoutes);

// Variables globales pour stocker les informations de la base
let databaseTables = [];
let databaseSchema = {};

// ==================== ROUTES GÉNÉRIQUES ====================

// Route pour obtenir le schéma complet de la base de données
app.get('/api/database/schema', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/database/schema');

    const schema = await detectDatabaseTables();
    const counts = await getTableCounts();

    // Mettre à jour les variables globales
    databaseTables = Object.keys(schema);
    databaseSchema = schema;

    res.json({
      success: true,
      data: {
        tables: databaseTables,
        schema: schema,
        counts: counts,
        total_tables: databaseTables.length
      }
    });
  } catch (error) {
    console.error('❌ Erreur schema:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du schéma',
      error: error.message
    });
  }
});

// Route dynamique pour consulter n'importe quelle table
app.get('/api/table/:tableName', async (req, res) => {
  try {
    const { tableName } = req.params;
    const { limit = 100, offset = 0 } = req.query;

    console.log(`📥 Requête GET /api/table/${tableName}`);

    // Vérifier que la table existe
    if (!databaseTables.includes(tableName.toLowerCase())) {
      return res.status(404).json({
        success: false,
        message: `Table '${tableName}' non trouvée`,
        available_tables: databaseTables
      });
    }

    // Récupérer les données de la table
    const result = await pool.query(`
      SELECT * FROM ${tableName}
      ORDER BY 1
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    // Compter le total d'enregistrements
    const countResult = await pool.query(`SELECT COUNT(*) as total FROM ${tableName}`);
    const total = parseInt(countResult.rows[0].total);

    console.log(`✅ ${result.rows.length} enregistrements récupérés de ${tableName}`);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        total: total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        has_more: (parseInt(offset) + parseInt(limit)) < total
      },
      table_info: {
        name: tableName,
        columns: databaseSchema[tableName] || []
      }
    });

  } catch (error) {
    console.error(`❌ Erreur table ${req.params.tableName}:`, error);
    res.status(500).json({
      success: false,
      message: `Erreur lors de la consultation de la table ${req.params.tableName}`,
      error: error.message
    });
  }
});

// Route pour exécuter une requête SQL personnalisée (avec sécurité)
app.post('/api/query', async (req, res) => {
  try {
    const { query, params = [] } = req.body;
    console.log('📥 Requête POST /api/query:', query);

    // Sécurité: autoriser seulement les requêtes SELECT
    if (!query.trim().toLowerCase().startsWith('select')) {
      return res.status(400).json({
        success: false,
        message: 'Seules les requêtes SELECT sont autorisées'
      });
    }

    const result = await pool.query(query, params);

    console.log(`✅ Requête exécutée: ${result.rows.length} résultats`);

    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      query: query
    });

  } catch (error) {
    console.error('❌ Erreur requête SQL:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'exécution de la requête',
      error: error.message
    });
  }
});

// ==================== ROUTES SPÉCIALISÉES POUR TECHNICIAN DASHBOARD ====================

// Route pour obtenir les statistiques du tableau de bord (dynamiques depuis la base)
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/dashboard/stats');

    // Exécuter toutes les requêtes en parallèle pour optimiser les performances
    const statsQueries = await Promise.all([
      // Total des interventions (consommations relevées)
      pool.query('SELECT COUNT(*) as count FROM consommation'),

      // Interventions en cours (consommations avec status "En cours")
      pool.query('SELECT COUNT(*) as count FROM consommation WHERE status = $1', ['active']),

      // Total des clients
      pool.query('SELECT COUNT(*) as count FROM client'),

      // Relevés d'aujourd'hui (consommations de la période actuelle)
      pool.query(`
        SELECT COUNT(*) as count
        FROM consommation
        WHERE periode = $1
      `, [new Date().toISOString().slice(0, 7)]) // Format YYYY-MM
    ]);

    const stats = {
      interventions: parseInt(statsQueries[0].rows[0].count),
      interventionsEnCours: parseInt(statsQueries[1].rows[0].count),
      clients: parseInt(statsQueries[2].rows[0].count),
      relevesAujourdhui: parseInt(statsQueries[3].rows[0].count)
    };

    console.log('✅ Statistiques calculées:', stats);

    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erreur lors du calcul des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du calcul des statistiques',
      error: error.message
    });
  }
});

// Route pour obtenir les données du dashboard technicien
app.get('/api/technician/dashboard/:techId', async (req, res) => {
  try {
    const { techId } = req.params;
    console.log(`📥 Requête GET /api/technician/dashboard/${techId}`);

    // Récupérer les informations du technicien
    const techResult = await pool.query(
      'SELECT * FROM utilisateur WHERE idtech = $1',
      [techId]
    );

    if (techResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Technicien non trouvé'
      });
    }

    const technicien = techResult.rows[0];

    const statsQueries = await Promise.all([
      // Nombre de consommations relevées par ce technicien
      pool.query('SELECT COUNT(*) as count FROM consommation WHERE idtech = $1', [techId]),

      // Nombre de clients dans sa zone (si applicable)
      pool.query('SELECT COUNT(*) as count FROM client'),

      // Dernières consommations relevées
      pool.query(`
        SELECT c.*, cl.nom as client_nom, cl.prenom as client_prenom, cont.codeqr
        FROM consommation c
        LEFT JOIN contract cont ON c.idcont = cont.idcontract
        LEFT JOIN client cl ON cont.idclient = cl.idclient
        WHERE c.idtech = $1
        ORDER BY c.periode DESC
        LIMIT 10
      `, [techId]),

      // Contrats liés aux consommations du technicien
      pool.query(`
        SELECT cont.*, cl.nom as client_nom, cl.prenom as client_prenom
        FROM contract cont
        LEFT JOIN consommation cons ON cons.idcont = cont.idcontract
        LEFT JOIN client cl ON cont.idclient = cl.idclient
        WHERE cons.idtech = $1
        ORDER BY cont.datecontract DESC
        LIMIT 10
      `, [techId])
    ]);

    const dashboardData = {
      technicien: technicien,
      statistiques: {
        consommations_relevees: parseInt(statsQueries[0].rows[0].count),
        total_clients: parseInt(statsQueries[1].rows[0].count),
        dernieres_consommations: statsQueries[2].rows,
        contrats_associes: statsQueries[3].rows
      }
    };

    console.log(`✅ Dashboard généré pour technicien ${techId}`);

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('❌ Erreur dashboard technicien:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du dashboard',
      error: error.message
    });
  }
});

// ==================== INITIALISATION ET DÉMARRAGE ====================

// Test de connexion et détection automatique au démarrage
async function initializeServer() {
  try {
    console.log('🚀 Initialisation du serveur TechnicianDashboard...');

    // Utiliser la fonction d'initialisation du module database
    await initializeDatabase();

    // Mettre à jour les variables globales
    const schema = await detectDatabaseTables();
    databaseTables = Object.keys(schema);
    databaseSchema = schema;

    console.log('\n✅ Serveur initialisé avec succès!');

  } catch (error) {
    console.error('❌ Erreur d\'initialisation:', error);
    process.exit(1);
  }
}

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur TechnicianDashboard modulaire fonctionnel',
    timestamp: new Date().toISOString(),
    database: process.env.DB_NAME || 'Facutration',
    tables_detected: databaseTables.length,
    tables: databaseTables,
    modules: ['clients', 'consommation', 'factures', 'codeQR', 'auth']
  });
});

// ==================== DÉMARRAGE DU SERVEUR ====================

const PORT = 3002; // Force le port 3002

initializeServer().then(() => {
  app.listen(PORT, () => {
    console.log(`\n🚀 Serveur TechnicianDashboard modulaire démarré sur http://localhost:${PORT}`);
    console.log(`📊 Base de données: ${process.env.DB_NAME || 'Facutration'}`);
    console.log(`📋 Tables détectées: ${databaseTables.length}`);
    console.log('\n📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/database/schema (schéma complet)');
    console.log('  - GET  /api/table/:tableName (consultation table)');
    console.log('  - POST /api/query (requête SQL)');
    console.log('  - GET  /api/dashboard/stats (statistiques dynamiques)');
    console.log('  - GET  /api/technician/dashboard/:techId');
    console.log('\n🔧 Modules spécialisés:');
    console.log('  - clients.js (gestion des clients)');
    console.log('  - consommation.js (gestion des consommations)');
    console.log('  - factures.js (gestion des factures)');
    console.log('  - codeQR.js (scanner QR)');
    console.log('  - auth.js (authentification)');
  });
}).catch(err => {
  console.error('❌ Erreur lors du démarrage du serveur:', err.message);
  process.exit(1);
});

module.exports = app;