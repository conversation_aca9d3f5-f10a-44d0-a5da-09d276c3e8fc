import React, { useState, useEffect } from 'react';
import './TechnicianDashboard.css';

// Configuration de l'API
const API_BASE_URL = 'http://localhost:3002';

const ListesClients = ({ onBack, onClientSelect }) => {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Fonction pour récupérer les clients depuis la base de données "Facutration"
  const fetchClients = async () => {
    try {
      setLoading(true);
      console.log('🔄 Récupération des clients depuis la base "Facutration"...');

      const response = await fetch(`${API_BASE_URL}/api/clients`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 Données reçues:', data);

      if (data.success && data.data) {
        setClients(data.data);
        console.log(`✅ ${data.data.length} clients chargés depuis la base "Facutration"`);
      } else {
        setClients([]);
        console.warn('⚠️ Aucun client trouvé dans la réponse');
      }
      setError(null);
    } catch (err) {
      console.error('❌ Erreur lors de la récupération des clients:', err);

      let errorMessage = 'Impossible de charger les clients depuis la base "Facutration".';

      if (err.message.includes('Failed to fetch')) {
        errorMessage += ' Vérifiez que le serveur backend est démarré sur le port 3002.';
      } else if (err.message.includes('HTTP')) {
        errorMessage += ` Erreur serveur: ${err.message}`;
      } else {
        errorMessage += ' Vérifiez votre connexion.';
      }

      setError(errorMessage);
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  // Filtrer les clients selon le terme de recherche (structure base "Facutration")
  const filteredClients = clients.filter(client =>
    client.nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.prenom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.adresse?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.ville?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.tel?.includes(searchTerm) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.idclient?.toString().includes(searchTerm)
  );

  const handleRefresh = () => {
    fetchClients();
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button
              onClick={onBack}
              className="tech-mobile-back-btn"
            >
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des clients...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button
              onClick={onBack}
              className="tech-mobile-back-btn"
            >
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
          </div>
          <div className="tech-mobile-error">
            <div className="tech-mobile-error-icon">⚠️</div>
            <h3>Erreur de chargement</h3>
            <p>{error}</p>
            <button
              onClick={handleRefresh}
              className="tech-mobile-action-btn complete"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button
            onClick={onBack}
            className="tech-mobile-back-btn"
          >
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Liste des Clients</h1>
            <p className="tech-mobile-card-subtitle">
              {filteredClients.length} client(s) trouvé(s)
            </p>
          </div>
          <button
            onClick={handleRefresh}
            className="tech-mobile-refresh-btn"
          >
            🔄
          </button>
        </div>
      </div>

      {/* Barre de recherche */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-search-container">
          <input
            type="text"
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="tech-mobile-search-input"
          />
          <div className="tech-mobile-search-icon">🔍</div>
        </div>
      </div>

      {/* Liste des clients */}
      {filteredClients.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">👥</div>
            <h3>Aucun client trouvé</h3>
            <p>
              {searchTerm
                ? 'Aucun client ne correspond à votre recherche.'
                : 'Aucun client enregistré dans la base de données.'
              }
            </p>
          </div>
        </div>
      ) : (
        filteredClients.map(client => (
          <div key={client.idclient} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <strong>{client.nom} {client.prenom}</strong>
              </div>
              <div className="tech-mobile-intervention-badge">
                ID: {client.idclient}
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              <div className="tech-mobile-intervention-info">
                <span>📍 {client.adresse}</span>
              </div>
              {client.ville && (
                <div className="tech-mobile-intervention-info">
                  <span>🏙️ {client.ville}</span>
                </div>
              )}
              {client.tel && (
                <div className="tech-mobile-intervention-info">
                  <span>📞 {client.tel}</span>
                </div>
              )}
              {client.email && (
                <div className="tech-mobile-intervention-info">
                  <span>📧 {client.email}</span>
                </div>
              )}
              {client.ids && (
                <div className="tech-mobile-intervention-info">
                  <span>🏢 Secteur: {client.ids}</span>
                </div>
              )}
            </div>

            <div className="tech-mobile-intervention-actions">
              <button
                className="tech-mobile-action-btn start"
                onClick={() => {
                  // Logique pour localiser le client via Google Maps
                  const searchQuery = encodeURIComponent(`${client.adresse}, ${client.ville || ''}`);
                  const url = `https://www.google.com/maps/search/${searchQuery}`;
                  window.open(url, '_blank');
                }}
              >
                Localiser
              </button>
              <button
                className="tech-mobile-action-btn complete"
                onClick={() => {
                  if (onClientSelect) {
                    onClientSelect(client);
                  }
                }}
              >
                Sélectionner
              </button>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ListesClients;