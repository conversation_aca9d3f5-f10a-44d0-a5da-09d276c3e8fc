const { Pool } = require('pg');

// Configuration de la base de données PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

async function testClientContractTables() {
  console.log('🔍 Test des tables Client et Contract dans la base "Facutration"...\n');

  try {
    const client = await pool.connect();
    console.log('✅ Connexion réussie à la base "Facutration"\n');

    // Test 1: Vérifier la table Client
    console.log('1️⃣ Test de la table Client...');
    const clientQuery = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
      LIMIT 5
    `;

    const clientResult = await client.query(clientQuery);
    console.log(`📊 ${clientResult.rows.length} clients trouvés:`);
    
    clientResult.rows.forEach((clientData, index) => {
      console.log(`   ${index + 1}. ${clientData.nom} ${clientData.prenom}`);
      console.log(`      ID: ${clientData.idclient}`);
      console.log(`      Ville: ${clientData.ville}`);
      console.log(`      Secteur: ${clientData.secteur_nom || 'Non défini'}`);
      console.log('      ───────────────────────────────────────');
    });
    console.log('');

    // Test 2: Vérifier la table Contract
    console.log('2️⃣ Test de la table Contract...');
    const contractQuery = `
      SELECT
        co.idcontract,
        co.codeqr,
        co.datecontract,
        co.marquecompteur,
        co.numseriecompteur,
        co.idclient
      FROM contract co
      ORDER BY co.idcontract
      LIMIT 5
    `;

    const contractResult = await client.query(contractQuery);
    console.log(`📋 ${contractResult.rows.length} contrats trouvés:`);
    
    contractResult.rows.forEach((contract, index) => {
      console.log(`   ${index + 1}. Contrat ID: ${contract.idcontract}`);
      console.log(`      Code QR: ${contract.codeqr}`);
      console.log(`      Client ID: ${contract.idclient}`);
      console.log(`      Marque compteur: ${contract.marquecompteur}`);
      console.log(`      Date: ${contract.datecontract}`);
      console.log('      ───────────────────────────────────────');
    });
    console.log('');

    // Test 3: Jointure Client + Contract (comme dans l'API)
    console.log('3️⃣ Test de la jointure Client + Contract...');
    const joinQuery = `
      SELECT 
        co.idcontract,
        co.codeqr,
        co.datecontract,
        co.marquecompteur,
        co.numseriecompteur,
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM contract co
      LEFT JOIN client c ON co.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
      LIMIT 5
    `;

    const joinResult = await client.query(joinQuery);
    console.log(`🔗 ${joinResult.rows.length} contrats avec clients trouvés:`);
    
    joinResult.rows.forEach((data, index) => {
      console.log(`   ${index + 1}. Client: ${data.nom} ${data.prenom}`);
      console.log(`      Contrat ID: ${data.idcontract}`);
      console.log(`      Code QR: ${data.codeqr}`);
      console.log(`      Marque compteur: ${data.marquecompteur}`);
      console.log(`      Ville: ${data.ville}`);
      console.log(`      Secteur: ${data.secteur_nom || 'Non défini'}`);
      console.log('      ───────────────────────────────────────');
    });
    console.log('');

    // Test 4: Compter les données
    console.log('4️⃣ Statistiques des tables...');
    
    const clientCountResult = await client.query('SELECT COUNT(*) as count FROM client');
    const contractCountResult = await client.query('SELECT COUNT(*) as count FROM contract');
    const secteurCountResult = await client.query('SELECT COUNT(*) as count FROM secteur');
    
    console.log(`📊 Nombre total de clients: ${clientCountResult.rows[0].count}`);
    console.log(`📋 Nombre total de contrats: ${contractCountResult.rows[0].count}`);
    console.log(`🏢 Nombre total de secteurs: ${secteurCountResult.rows[0].count}`);

    // Test 5: Vérifier les relations
    console.log('\n5️⃣ Vérification des relations...');
    
    const relationQuery = `
      SELECT 
        c.idclient,
        c.nom,
        c.prenom,
        COUNT(co.idcontract) as nombre_contrats
      FROM client c
      LEFT JOIN contract co ON c.idclient = co.idclient
      GROUP BY c.idclient, c.nom, c.prenom
      HAVING COUNT(co.idcontract) > 0
      ORDER BY nombre_contrats DESC
      LIMIT 5
    `;

    const relationResult = await client.query(relationQuery);
    console.log(`🔗 Clients avec contrats:`);
    
    relationResult.rows.forEach((data, index) => {
      console.log(`   ${index + 1}. ${data.nom} ${data.prenom} - ${data.nombre_contrats} contrat(s)`);
    });

    client.release();
    console.log('\n🎉 Test terminé avec succès !');
    
    console.log('\n📋 Résumé pour l\'API:');
    console.log('✅ Table Client accessible avec secteurs');
    console.log('✅ Table Contract accessible');
    console.log('✅ Jointure Client + Contract fonctionnelle');
    console.log('✅ Données prêtes pour les champs Client * et Contrat *');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
    console.error('📋 Détails:', error);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testClientContractTables();
