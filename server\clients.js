const express = require('express');
const { pool } = require('./database');

const router = express.Router();

// Route pour récupérer tous les clients
router.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} clients récupérés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer un client spécifique par ID
router.get('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/clients/${id}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log(`✅ Client ${id} récupéré`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: error.message
    });
  }
});

// Fonction pour générer un code QR unique
function generateQRCode(clientId, nom, prenom) {
  // Format: AQ-[ID]-[3 premières lettres du nom]-[3 premières lettres du prénom]-[timestamp]
  const nomCode = nom.substring(0, 3).toUpperCase();
  const prenomCode = prenom.substring(0, 3).toUpperCase();
  const timestamp = Date.now().toString().slice(-6); // 6 derniers chiffres du timestamp
  return `AQ-${clientId}-${nomCode}-${prenomCode}-${timestamp}`;
}

// Route pour ajouter un nouveau client avec génération automatique de contrat et QR Code
router.post('/api/clients', async (req, res) => {
  const client = await pool.connect();

  try {
    console.log('📥 Requête POST /api/clients:', req.body);
    const { nom, prenom, adresse, ville, tel, email, ids, marqueCompteur, numSerieCompteur, posX, posY } = req.body;

    // Validation des champs requis
    if (!nom || !prenom || !adresse) {
      return res.status(400).json({
        success: false,
        message: 'Les champs nom, prénom et adresse sont requis'
      });
    }

    // Démarrer une transaction
    await client.query('BEGIN');

    // 1. Insérer le client
    const clientQuery = `
      INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const clientValues = [nom, prenom, adresse, ville, tel, email, ids];
    const clientResult = await client.query(clientQuery, clientValues);
    const newClient = clientResult.rows[0];

    console.log('✅ Nouveau client ajouté:', newClient);

    // 2. Générer le code QR unique
    const qrCode = generateQRCode(newClient.idclient, nom, prenom);
    console.log('🔗 Code QR généré:', qrCode);

    // 3. Créer automatiquement un contrat avec le code QR
    const contractQuery = `
      INSERT INTO contract (codeqr, datecontract, idclient, marquecompteur, numseriecompteur, posx, posy)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const contractValues = [
      qrCode,
      new Date(), // datecontract
      newClient.idclient, // idclient
      marqueCompteur || null, // marquecompteur (optionnel)
      numSerieCompteur || null, // numseriecompteur (optionnel)
      posX || null, // posx (optionnel)
      posY || null  // posy (optionnel)
    ];

    const contractResult = await client.query(contractQuery, contractValues);
    const newContract = contractResult.rows[0];

    console.log('✅ Contrat créé avec QR Code:', newContract);

    // Valider la transaction
    await client.query('COMMIT');

    // Retourner les données du client et du contrat
    res.status(201).json({
      success: true,
      data: {
        client: newClient,
        contract: newContract,
        qrCode: qrCode
      },
      message: 'Client ajouté avec succès et QR Code généré'
    });

  } catch (error) {
    // Annuler la transaction en cas d'erreur
    await client.query('ROLLBACK');
    console.error('❌ Erreur lors de l\'ajout du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout du client',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Route pour mettre à jour un client
router.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête PUT /api/clients/${id}:`, req.body);
    const { nom, prenom, adresse, ville, tel, email, ids } = req.body;

    const query = `
      UPDATE client 
      SET nom = $1, prenom = $2, adresse = $3, ville = $4, tel = $5, email = $6, ids = $7
      WHERE idclient = $8
      RETURNING *
    `;

    const values = [nom, prenom, adresse, ville, tel, email, ids, id];
    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log('✅ Client mis à jour:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du client',
      error: error.message
    });
  }
});

// Route pour supprimer un client
router.delete('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête DELETE /api/clients/${id}`);

    const query = 'DELETE FROM client WHERE idclient = $1 RETURNING *';
    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log('✅ Client supprimé:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client supprimé avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la suppression du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du client',
      error: error.message
    });
  }
});

// Route pour rechercher des clients
router.get('/api/clients/search/:term', async (req, res) => {
  try {
    const { term } = req.params;
    console.log(`📥 Recherche de clients: ${term}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE
        LOWER(c.nom) LIKE LOWER($1) OR
        LOWER(c.prenom) LIKE LOWER($1) OR
        LOWER(c.email) LIKE LOWER($1) OR
        c.tel LIKE $1
      ORDER BY c.nom, c.prenom
    `;

    const searchTerm = `%${term}%`;
    const result = await pool.query(query, [searchTerm]);

    console.log(`✅ ${result.rows.length} clients trouvés pour "${term}"`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s) pour "${term}"`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la recherche de clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la recherche de clients',
      error: error.message
    });
  }
});

// Route pour récupérer le QR Code d'un client
router.get('/api/clients/:id/qrcode', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête QR Code pour client: ${id}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        cont.codeqr,
        cont.idcontract,
        cont.datecontract,
        cont.marquecompteur,
        cont.numseriecompteur
      FROM client c
      LEFT JOIN contract cont ON c.idclient = cont.idclient
      WHERE c.idclient = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    const clientData = result.rows[0];

    if (!clientData.codeqr) {
      return res.status(404).json({
        success: false,
        message: 'Aucun QR Code trouvé pour ce client'
      });
    }

    console.log(`✅ QR Code trouvé pour ${clientData.nom} ${clientData.prenom}: ${clientData.codeqr}`);

    res.json({
      success: true,
      data: {
        client: {
          id: clientData.idclient,
          nom: clientData.nom,
          prenom: clientData.prenom
        },
        contract: {
          id: clientData.idcontract,
          qrCode: clientData.codeqr,
          dateContract: clientData.datecontract,
          marqueCompteur: clientData.marquecompteur,
          numSerieCompteur: clientData.numseriecompteur
        }
      },
      message: `QR Code récupéré pour ${clientData.nom} ${clientData.prenom}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du QR Code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du QR Code',
      error: error.message
    });
  }
});

// Route pour générer un nouveau QR Code pour un client existant
router.post('/api/clients/:id/generate-qrcode', async (req, res) => {
  const client = await pool.connect();

  try {
    const { id } = req.params;
    const { marqueCompteur, numSerieCompteur, posX, posY } = req.body;
    console.log(`📥 Génération QR Code pour client: ${id}`);

    await client.query('BEGIN');

    // Récupérer les informations du client
    const clientQuery = 'SELECT * FROM client WHERE idclient = $1';
    const clientResult = await client.query(clientQuery, [id]);

    if (clientResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    const clientData = clientResult.rows[0];

    // Vérifier s'il existe déjà un contrat
    const existingContractQuery = 'SELECT * FROM contract WHERE idclient = $1';
    const existingContractResult = await client.query(existingContractQuery, [id]);

    if (existingContractResult.rows.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Un QR Code existe déjà pour ce client',
        data: {
          qrCode: existingContractResult.rows[0].codeqr
        }
      });
    }

    // Générer un nouveau QR Code
    const qrCode = generateQRCode(clientData.idclient, clientData.nom, clientData.prenom);

    // Créer le contrat avec le QR Code
    const contractQuery = `
      INSERT INTO contract (codeqr, datecontract, idclient, marquecompteur, numseriecompteur, posx, posy)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const contractValues = [
      qrCode,
      new Date(),
      clientData.idclient,
      marqueCompteur || null,
      numSerieCompteur || null,
      posX || null,
      posY || null
    ];

    const contractResult = await client.query(contractQuery, contractValues);
    const newContract = contractResult.rows[0];

    await client.query('COMMIT');

    console.log(`✅ QR Code généré pour ${clientData.nom} ${clientData.prenom}: ${qrCode}`);

    res.status(201).json({
      success: true,
      data: {
        client: clientData,
        contract: newContract,
        qrCode: qrCode
      },
      message: `QR Code généré avec succès pour ${clientData.nom} ${clientData.prenom}`
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Erreur lors de la génération du QR Code:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération du QR Code',
      error: error.message
    });
  } finally {
    client.release();
  }
});

module.exports = router;
