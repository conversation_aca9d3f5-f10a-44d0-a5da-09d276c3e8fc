const express = require('express');
const { pool } = require('./database');

const router = express.Router();

// Route pour récupérer tous les clients
router.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients');

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} clients récupérés`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s)`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer un client spécifique par ID
router.get('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête GET /api/clients/${id}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE c.idclient = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log(`✅ Client ${id} récupéré`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: error.message
    });
  }
});

// Route pour ajouter un nouveau client
router.post('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête POST /api/clients:', req.body);
    const { nom, prenom, adresse, ville, tel, email, ids } = req.body;

    // Validation des champs requis
    if (!nom || !prenom || !adresse) {
      return res.status(400).json({
        success: false,
        message: 'Les champs nom, prénom et adresse sont requis'
      });
    }

    const query = `
      INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const values = [nom, prenom, adresse, ville, tel, email, ids];
    const result = await pool.query(query, values);

    console.log('✅ Nouveau client ajouté:', result.rows[0]);
    res.status(201).json({
      success: true,
      data: result.rows[0],
      message: 'Client ajouté avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'ajout du client',
      error: error.message
    });
  }
});

// Route pour mettre à jour un client
router.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête PUT /api/clients/${id}:`, req.body);
    const { nom, prenom, adresse, ville, tel, email, ids } = req.body;

    const query = `
      UPDATE client 
      SET nom = $1, prenom = $2, adresse = $3, ville = $4, tel = $5, email = $6, ids = $7
      WHERE idclient = $8
      RETURNING *
    `;

    const values = [nom, prenom, adresse, ville, tel, email, ids, id];
    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log('✅ Client mis à jour:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du client',
      error: error.message
    });
  }
});

// Route pour supprimer un client
router.delete('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📥 Requête DELETE /api/clients/${id}`);

    const query = 'DELETE FROM client WHERE idclient = $1 RETURNING *';
    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log('✅ Client supprimé:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Client supprimé avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la suppression du client:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du client',
      error: error.message
    });
  }
});

// Route pour rechercher des clients
router.get('/api/clients/search/:term', async (req, res) => {
  try {
    const { term } = req.params;
    console.log(`📥 Recherche de clients: ${term}`);

    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom,
        s.ids as secteur_id
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      WHERE 
        LOWER(c.nom) LIKE LOWER($1) OR 
        LOWER(c.prenom) LIKE LOWER($1) OR
        LOWER(c.email) LIKE LOWER($1) OR
        c.tel LIKE $1
      ORDER BY c.nom, c.prenom
    `;

    const searchTerm = `%${term}%`;
    const result = await pool.query(query, [searchTerm]);

    console.log(`✅ ${result.rows.length} clients trouvés pour "${term}"`);
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length,
      message: `${result.rows.length} client(s) trouvé(s) pour "${term}"`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la recherche de clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la recherche de clients',
      error: error.message
    });
  }
});

module.exports = router;
