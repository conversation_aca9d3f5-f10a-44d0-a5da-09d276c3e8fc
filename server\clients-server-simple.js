const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',
  port: 5432,
});

console.log('🚀 Démarrage du serveur clients simplifié...');

// Test de connexion
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
  } else {
    console.log('✅ Connexion réussie à la base de données "Facutration"');
    release();
  }
});

// Route de test
app.get('/', (req, res) => {
  console.log('📡 Route / appelée');
  res.json({
    message: 'Serveur clients fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  console.log('📥 GET /api/clients appelée');
  
  try {
    const query = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.ids,
        s.nom as secteur_nom
      FROM client c
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;

    console.log('🔍 Exécution de la requête SQL...');
    const result = await pool.query(query);
    
    console.log(`📊 ${result.rows.length} clients récupérés`);
    
    res.json({
      success: true,
      message: 'Clients récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });

  } catch (err) {
    console.error('❌ Erreur lors de la récupération des clients:', err.message);
    
    // Essayer sans JOIN en cas d'erreur
    try {
      console.log('🔄 Tentative sans JOIN...');
      const simpleQuery = `
        SELECT idclient, nom, prenom, adresse, ville, tel, email, ids
        FROM client
        ORDER BY nom, prenom
      `;
      
      const simpleResult = await pool.query(simpleQuery);
      console.log(`📊 ${simpleResult.rows.length} clients récupérés (sans JOIN)`);
      
      res.json({
        success: true,
        message: 'Clients récupérés avec succès (mode simple)',
        count: simpleResult.rows.length,
        data: simpleResult.rows
      });
      
    } catch (simpleErr) {
      console.error('❌ Erreur même avec requête simple:', simpleErr.message);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des clients',
        error: simpleErr.message
      });
    }
  }
});

// Route pour récupérer un client par ID
app.get('/api/clients/:id', async (req, res) => {
  console.log(`📥 GET /api/clients/${req.params.id} appelée`);
  
  try {
    const clientId = parseInt(req.params.id);
    
    if (isNaN(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'ID client invalide'
      });
    }

    const query = `
      SELECT idclient, nom, prenom, adresse, ville, tel, email, ids
      FROM client
      WHERE idclient = $1
    `;

    const result = await pool.query(query, [clientId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Client non trouvé'
      });
    }

    console.log(`👤 Client ID ${clientId} récupéré`);
    res.json({
      success: true,
      message: 'Client récupéré avec succès',
      data: result.rows[0]
    });

  } catch (err) {
    console.error('❌ Erreur lors de la récupération du client:', err.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du client',
      error: err.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur clients démarré sur http://localhost:${PORT}`);
  console.log('📊 Base de données: Facutration');
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/clients (tous les clients)');
  console.log('  - GET /api/clients/:id (client par ID)');
  console.log('🎯 Le serveur est prêt !');
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});

module.exports = app;
