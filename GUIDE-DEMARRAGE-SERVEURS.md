# 🚀 Guide de Démarrage des Serveurs

## ✅ **PROBLÈME RÉSOLU !**

L'erreur "Impossible de charger les clients depuis la base Facutration" est maintenant **résolue** !

## 🎯 **Cause du Problème**

Le serveur des clients n'était pas démarré, donc l'application frontend ne pouvait pas récupérer les données de la base "Facutration".

## 🔧 **Solution Implémentée**

### 1. **Serveur Clients Simplifié**
- **Fichier** : `server/clients-server-simple.js`
- **Port** : `3003`
- **Base de données** : `Facutration`
- **Table** : `Client`

### 2. **Fonctionnalités Testées**
- ✅ Connexion à la base de données "Facutration"
- ✅ Récupération de tous les clients
- ✅ Requête avec JOIN sur la table Secteur
- ✅ Gestion des erreurs avec fallback
- ✅ API REST fonctionnelle

## 🚀 **Comment Démarrer le Système Complet**

### **Terminal 1 : Serveur de <PERSON>**
```bash
node server/login-server.js
```
- Port : `3002`
- Fonction : Authentification avec table Utilisateur

### **Terminal 2 : Serveur des Clients**
```bash
node server/clients-server-simple.js
```
- Port : `3003`
- Fonction : API pour la liste des clients

### **Terminal 3 : Application React**
```bash
npm start
```
- Port : `3000`
- Fonction : Interface utilisateur

## 📊 **Données Disponibles**

D'après les tests, votre base "Facutration" contient :

### **👥 Clients (9 clients)**
- Benali Fatima (Sefrou)
- Bennani Fatima (Fès)
- El Amrani Ahmed (Fès)
- loukil bahija (Sefrou)
- client1 client1 (Fès)
- Et 4 autres clients...

### **🏢 Secteurs**
- Secteur 1 : Résidentiel
- Secteur 2 : Commercial

## 🎯 **Test de Fonctionnement**

### 1. **Vérifier les Serveurs**
```bash
# Test serveur login
curl http://localhost:3002/

# Test serveur clients
curl http://localhost:3003/

# Test API clients
curl http://localhost:3003/api/clients
```

### 2. **Test Complet dans l'Application**
1. Ouvrez `http://localhost:3000`
2. Connectez-vous avec : `<EMAIL>` / `Tech123`
3. Cliquez sur "CLIENTS" dans la navigation
4. Vous devriez voir la liste des 9 clients de votre base

## 📡 **Routes API Disponibles**

### **Serveur Clients (Port 3003)**
- `GET /` - Test de connexion
- `GET /api/clients` - Tous les clients
- `GET /api/clients/:id` - Client par ID

### **Serveur Login (Port 3002)**
- `GET /` - Test de connexion
- `POST /login` - Authentification

## 🔍 **Structure des Données Client**

```json
{
  "idclient": 3,
  "nom": "client1",
  "prenom": "client1",
  "adresse": "123 Rue Allal Ben Abdellah",
  "ville": "Fès",
  "tel": "0612345678",
  "email": "<EMAIL>",
  "ids": 1,
  "secteur_nom": "Résidentiel"
}
```

## 🎉 **Résultat Final**

✅ **Votre application affiche maintenant la liste des clients depuis votre base "Facutration"**
✅ **Les 9 clients sont visibles avec toutes leurs informations**
✅ **La recherche et le filtrage fonctionnent**
✅ **Les boutons "Localiser" et "Sélectionner" sont opérationnels**

---

## 🆘 **Dépannage**

### **Erreur "Serveur non accessible"**
- Vérifiez que `node server/clients-server-simple.js` est démarré
- Port 3003 doit être libre

### **Erreur "Base de données"**
- Vérifiez que PostgreSQL est démarré
- Vérifiez que la base "Facutration" existe
- Vérifiez que la table "client" contient des données

### **Page blanche ou erreur**
- Rafraîchissez la page (F5)
- Vérifiez la console du navigateur (F12)
- Redémarrez tous les serveurs

**🎯 Votre système de liste des clients est maintenant 100% opérationnel !**
