import React, { useState, useEffect } from 'react';
import './clients.css';

const ClientsPage = () => {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Fonction pour récupérer les clients depuis l'API
  const fetchClients = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3002/api/clients');
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setClients(data.data);
        setError(null);
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des clients');
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des clients:', err);
      setError(err.message);
      setClients([]);
    } finally {
      setLoading(false);
    }
  };

  // Fonction de recherche
  const searchClients = async (term) => {
    if (!term.trim()) {
      fetchClients();
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`http://localhost:3002/api/clients/search/${encodeURIComponent(term)}`);
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setClients(data.data);
        setError(null);
      } else {
        throw new Error(data.message || 'Erreur lors de la recherche');
      }
    } catch (err) {
      console.error('Erreur lors de la recherche:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Charger les clients au montage du composant
  useEffect(() => {
    fetchClients();
  }, []);

  // Gérer la recherche
  const handleSearch = (e) => {
    const term = e.target.value;
    setSearchTerm(term);
    
    // Recherche avec délai pour éviter trop de requêtes
    const timeoutId = setTimeout(() => {
      searchClients(term);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  // Fonction pour retourner au dashboard
  const goBack = () => {
    window.history.back();
  };

  if (loading) {
    return (
      <div className="clients-container">
        <div className="loading">
          <div className="spinner"></div>
          <p>Chargement des clients...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="clients-container">
      {/* Header */}
      <div className="clients-header">
        <div className="header-top">
          <button className="back-button" onClick={goBack}>
            ← Retour
          </button>
          <h1>Liste des Clients</h1>
        </div>
        
        {/* Barre de recherche */}
        <div className="search-section">
          <div className="search-box">
            <input
              type="text"
              placeholder="Rechercher un client..."
              value={searchTerm}
              onChange={handleSearch}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
          <div className="clients-count">
            {clients.length} client(s) trouvé(s)
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="clients-content">
        {error && (
          <div className="error-message">
            <p>❌ Erreur: {error}</p>
            <button onClick={fetchClients} className="retry-button">
              Réessayer
            </button>
          </div>
        )}

        {!error && clients.length === 0 && !loading && (
          <div className="no-clients">
            <p>Aucun client trouvé</p>
            {searchTerm && (
              <button onClick={() => {setSearchTerm(''); fetchClients();}} className="clear-search">
                Effacer la recherche
              </button>
            )}
          </div>
        )}

        {!error && clients.length > 0 && (
          <div className="clients-grid">
            {clients.map((client) => (
              <div key={client.idclient} className="client-card">
                <div className="client-header">
                  <h3>{client.nom} {client.prenom}</h3>
                  <span className="client-id">ID: {client.idclient}</span>
                </div>
                
                <div className="client-info">
                  <div className="info-row">
                    <span className="icon">📍</span>
                    <span>{client.adresse}</span>
                  </div>
                  
                  {client.ville && (
                    <div className="info-row">
                      <span className="icon">🏙️</span>
                      <span>{client.ville}</span>
                    </div>
                  )}
                  
                  {client.tel && (
                    <div className="info-row">
                      <span className="icon">📞</span>
                      <span>{client.tel}</span>
                    </div>
                  )}
                  
                  {client.email && (
                    <div className="info-row">
                      <span className="icon">📧</span>
                      <span>{client.email}</span>
                    </div>
                  )}
                  
                  {client.secteur_nom && (
                    <div className="info-row">
                      <span className="icon">🏢</span>
                      <span>Secteur: {client.secteur_nom}</span>
                    </div>
                  )}
                </div>
                
                <div className="client-actions">
                  <button className="action-btn view-btn">
                    👁️ Voir
                  </button>
                  <button className="action-btn edit-btn">
                    ✏️ Modifier
                  </button>
                  <button className="action-btn qr-btn">
                    📱 QR Code
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientsPage;
