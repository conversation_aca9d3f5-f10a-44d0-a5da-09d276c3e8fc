const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');

const app = express();
const port = 3004; // Port du backend pour saisie client

// Configuration de la connexion PostgreSQL "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration',
  password: '123456',  // Mot de passe PostgreSQL
  port: 5432
});

// Test de connexion à la base de données
console.log('🔄 Tentative de connexion à la base de données Facutration...');
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données Facutration:', err);
    console.error('   Détails:', err.message);
  } else {
    console.log('✅ Connexion réussie à la base de données Facutration');
    release();
  }
});

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// ==================== ROUTES API ====================

// Route de test
app.get('/', (req, res) => {
  console.log('📡 Route / appelée');
  res.json({
    message: 'Serveur saisie client fonctionnel',
    database: 'Facutration',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📋 Récupération de tous les clients...');
    const client = await pool.connect();

    const query = `
      SELECT idclient, nom, prenom, adresse, ville, tel, email, ids
      FROM client
      ORDER BY nom, prenom
    `;

    const result = await client.query(query);
    client.release();

    console.log(`✅ ${result.rows.length} clients récupérés`);
    res.json({
      success: true,
      message: 'Clients récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération des clients'
    });
  }
});

// Route POST pour ajouter un client
app.post('/api/clients', async (req, res) => {
  try {
    console.log('➕ Ajout d\'un nouveau client...');
    console.log('Données reçues:', req.body);

    const { nom, prenom, adresse, ville, tel, email, ids } = req.body;

    // Validation des champs obligatoires (seulement nom et prénom)
    if (!nom || !prenom) {
      return res.status(400).json({
        success: false,
        error: "Les champs nom et prénom sont obligatoires."
      });
    }

    const client = await pool.connect();

    const insertQuery = `
      INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const values = [
      nom.trim(),
      prenom.trim(),
      adresse ? adresse.trim() : null,
      ville ? ville.trim() : null,
      tel ? tel.trim() : null,
      email ? email.trim() : null,
      ids || null
    ];

    const result = await client.query(insertQuery, values);
    client.release();

    const newClient = result.rows[0];
    console.log(`✅ Client ajouté avec succès - ID: ${newClient.idclient}`);
    console.log(`   Nom: ${newClient.prenom} ${newClient.nom}`);
    console.log(`   Ville: ${newClient.ville || 'Non spécifiée'}`);

    res.status(201).json({
      success: true,
      message: `Client ${newClient.prenom} ${newClient.nom} ajouté avec succès`,
      data: newClient
    });
  } catch (error) {
    console.error("❌ Erreur lors de l'insertion du client :", error);

    // Gestion des erreurs spécifiques
    if (error.code === '23505') { // Violation de contrainte unique
      res.status(400).json({
        success: false,
        error: 'Un client avec ces informations existe déjà'
      });
    } else {
      res.status(500).json({
        success: false,
        error: "Erreur serveur lors de l'ajout du client"
      });
    }
  }
});

// Route pour récupérer un client par ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const clientId = parseInt(req.params.id);
    console.log(`🔍 Recherche du client ID: ${clientId}`);

    if (isNaN(clientId)) {
      return res.status(400).json({
        success: false,
        error: 'ID client invalide'
      });
    }

    const client = await pool.connect();

    const query = `
      SELECT idclient, nom, prenom, adresse, ville, tel, email, ids
      FROM client
      WHERE idclient = $1
    `;

    const result = await client.query(query, [clientId]);
    client.release();

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Client non trouvé'
      });
    }

    console.log(`✅ Client trouvé: ${result.rows[0].prenom} ${result.rows[0].nom}`);
    res.json({
      success: true,
      message: 'Client trouvé',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Erreur lors de la recherche du client:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la recherche du client'
    });
  }
});

// Route pour récupérer les secteurs (si la table existe)
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📋 Récupération des secteurs...');
    const client = await pool.connect();

    // Vérifier si la table secteur existe
    const checkTableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'secteur'
      );
    `;

    const tableExists = await client.query(checkTableQuery);

    if (tableExists.rows[0].exists) {
      const query = `SELECT ids, nomsecteur FROM secteur ORDER BY nomsecteur`;
      const result = await client.query(query);

      client.release();

      console.log(`✅ ${result.rows.length} secteurs récupérés`);
      res.json({
        success: true,
        message: 'Secteurs récupérés avec succès',
        count: result.rows.length,
        data: result.rows
      });
    } else {
      client.release();

      // Retourner des secteurs par défaut
      console.log('⚠️ Table secteur non trouvée, utilisation de secteurs par défaut');
      res.json({
        success: true,
        message: 'Secteurs par défaut utilisés',
        count: 3,
        data: [
          { ids: 1, nomsecteur: 'Centre-ville' },
          { ids: 2, nomsecteur: 'Banlieue Nord' },
          { ids: 3, nomsecteur: 'Banlieue Sud' }
        ]
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération des secteurs'
    });
  }
});

// Gestion des erreurs globales
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});

// Lancer le serveur
app.listen(port, () => {
  console.log(`✅ Serveur saisie client démarré sur http://localhost:${port}`);
  console.log(`📊 Base de données: Facutration`);
  console.log(`🔗 API disponible sur: http://localhost:${port}/api/clients`);
});
